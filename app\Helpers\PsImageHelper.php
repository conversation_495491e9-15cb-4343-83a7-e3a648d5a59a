<?php

namespace App\Helpers;

class PsImageHelper
{
    /**
     * Get PlayStation image based on PS type
     */
    public static function getPsImage($psType, $customImage = null)
    {
        // If custom image is provided and exists, use it
        if ($customImage && file_exists(public_path('storage/' . $customImage))) {
            return asset('storage/' . $customImage);
        }

        // Normalize the PS type string
        $normalizedType = strtolower(trim($psType));

        $imageMap = [
            // Standard names
            'PlayStation 4' => 'ps4.jpg',
            'PlayStation 4 Pro' => 'ps4-pro.jpg',
            'PlayStation 4 Slim' => 'ps4-slim.jpg',
            'PlayStation 5' => 'ps5.jpg',
            'PlayStation 5 Pro' => 'ps5-pro.jpg',

            // Lowercase variations
            'playstation 4' => 'ps4.jpg',
            'playstation 4 pro' => 'ps4-pro.jpg',
            'playstation 4 slim' => 'ps4-slim.jpg',
            'playstation 5' => 'ps5.jpg',
            'playstation 5 pro' => 'ps5-pro.jpg',

            // Short forms
            'ps4' => 'ps4.jpg',
            'ps4 pro' => 'ps4-pro.jpg',
            'ps4 slim' => 'ps4-slim.jpg',
            'ps5' => 'ps5.jpg',
            'ps5 pro' => 'ps5-pro.jpg',

            // Underscore variations
            'ps4_pro' => 'ps4-pro.jpg',
            'ps4_slim' => 'ps4-slim.jpg',
            'ps5_pro' => 'ps5-pro.jpg',
        ];

        // Try exact match first
        if (isset($imageMap[$psType])) {
            $imageName = $imageMap[$psType];
        } elseif (isset($imageMap[$normalizedType])) {
            $imageName = $imageMap[$normalizedType];
        } else {
            // Try partial matching for more flexibility
            if (strpos($normalizedType, 'ps4') !== false) {
                if (strpos($normalizedType, 'pro') !== false) {
                    $imageName = 'ps4-pro.jpg';
                } elseif (strpos($normalizedType, 'slim') !== false) {
                    $imageName = 'ps4-slim.jpg';
                } else {
                    $imageName = 'ps4.jpg';
                }
            } elseif (strpos($normalizedType, 'ps5') !== false) {
                if (strpos($normalizedType, 'pro') !== false) {
                    $imageName = 'ps5-pro.jpg';
                } else {
                    $imageName = 'ps5.jpg';
                }
            } else {
                $imageName = 'placeholder.svg';
            }
        }

        $imagePath = public_path('images/ps-units/' . $imageName);

        // Check if specific image exists, if not use placeholder
        if (!file_exists($imagePath)) {
            $imageName = 'placeholder.svg';
        }

        return asset('images/ps-units/' . $imageName);
    }

    /**
     * Get PlayStation display name
     */
    public static function getPsDisplayName($psType)
    {
        // Normalize the PS type string
        $normalizedType = strtolower(trim($psType));

        $displayMap = [
            // Standard names
            'PlayStation 4' => 'PlayStation 4',
            'PlayStation 4 Pro' => 'PlayStation 4 Pro',
            'PlayStation 4 Slim' => 'PlayStation 4 Slim',
            'PlayStation 5' => 'PlayStation 5',
            'PlayStation 5 Pro' => 'PlayStation 5 Pro',

            // Lowercase variations
            'playstation 4' => 'PlayStation 4',
            'playstation 4 pro' => 'PlayStation 4 Pro',
            'playstation 4 slim' => 'PlayStation 4 Slim',
            'playstation 5' => 'PlayStation 5',
            'playstation 5 pro' => 'PlayStation 5 Pro',

            // Short forms
            'ps4' => 'PlayStation 4',
            'ps4 pro' => 'PlayStation 4 Pro',
            'ps4 slim' => 'PlayStation 4 Slim',
            'ps5' => 'PlayStation 5',
            'ps5 pro' => 'PlayStation 5 Pro',

            // Underscore variations
            'ps4_pro' => 'PlayStation 4 Pro',
            'ps4_slim' => 'PlayStation 4 Slim',
            'ps5_pro' => 'PlayStation 5 Pro',
        ];

        // Try exact match first
        if (isset($displayMap[$psType])) {
            return $displayMap[$psType];
        } elseif (isset($displayMap[$normalizedType])) {
            return $displayMap[$normalizedType];
        } else {
            // Try partial matching for more flexibility
            if (strpos($normalizedType, 'ps4') !== false) {
                if (strpos($normalizedType, 'pro') !== false) {
                    return 'PlayStation 4 Pro';
                } elseif (strpos($normalizedType, 'slim') !== false) {
                    return 'PlayStation 4 Slim';
                } else {
                    return 'PlayStation 4';
                }
            } elseif (strpos($normalizedType, 'ps5') !== false) {
                if (strpos($normalizedType, 'pro') !== false) {
                    return 'PlayStation 5 Pro';
                } else {
                    return 'PlayStation 5';
                }
            } else {
                return $psType; // Return original if no match
            }
        }
    }

    /**
     * Get all available PlayStation types with their images
     */
    public static function getAllPsTypes()
    {
        return [
            'PlayStation 4' => [
                'name' => 'PlayStation 4',
                'image' => self::getPsImage('PlayStation 4'),
                'description' => 'Konsol gaming generasi ke-8 dari Sony'
            ],
            'PlayStation 4 Pro' => [
                'name' => 'PlayStation 4 Pro',
                'image' => self::getPsImage('PlayStation 4 Pro'),
                'description' => 'Versi upgrade PS4 dengan performa 4K'
            ],
            'PlayStation 4 Slim' => [
                'name' => 'PlayStation 4 Slim',
                'image' => self::getPsImage('PlayStation 4 Slim'),
                'description' => 'Versi compact dan hemat energi PS4'
            ],
            'PlayStation 5' => [
                'name' => 'PlayStation 5',
                'image' => self::getPsImage('PlayStation 5'),
                'description' => 'Konsol gaming terbaru dengan teknologi SSD'
            ],
            'PlayStation 5 Pro' => [
                'name' => 'PlayStation 5 Pro',
                'image' => self::getPsImage('PlayStation 5 Pro'),
                'description' => 'Versi Pro PS5 dengan performa maksimal'
            ],
        ];
    }

    /**
     * Check if image file exists
     */
    public static function imageExists($psType)
    {
        $imageMap = [
            'PlayStation 4' => 'ps4.jpg',
            'PlayStation 4 Pro' => 'ps4-pro.jpg',
            'PlayStation 4 Slim' => 'ps4-slim.jpg',
            'PlayStation 5' => 'ps5.jpg',
            'PlayStation 5 Pro' => 'ps5-pro.jpg',
        ];

        $imageName = $imageMap[$psType] ?? 'ps4.jpg';
        $imagePath = public_path('images/ps-units/' . $imageName);
        
        return file_exists($imagePath);
    }
}
