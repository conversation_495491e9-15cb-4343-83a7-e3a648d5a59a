@extends('layouts.app')

@section('title', 'Edit Rental - Rental PS Admin')
@section('page-title', 'Edit Rental')

@section('content')
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-edit me-2"></i>
                    Edit Rental - {{ $rental->rental_code }}
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('admin.rentals.update', $rental) }}">
                    @csrf
                    @method('PUT')
                    
                    <!-- Rental Information (Read Only) -->
                    <div class="row mb-4">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Kode Rental</label>
                                <input type="text" class="form-control" value="{{ $rental->rental_code }}" readonly>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Customer</label>
                                <input type="text" class="form-control" 
                                       value="{{ $rental->customer->name }} - {{ $rental->customer->phone }}" readonly>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">PS Unit</label>
                                <input type="text" class="form-control" 
                                       value="{{ $rental->psUnit->unit_code }} - {{ $rental->psUnit->ps_type }}" readonly>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Tipe Rental</label>
                                <input type="text" class="form-control" 
                                       value="{{ $rental->rental_type === 'hourly' ? 'Per Jam' : 'Per Hari' }}" readonly>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Durasi</label>
                                <input type="text" class="form-control" 
                                       value="{{ $rental->duration }} {{ $rental->rental_type === 'hourly' ? 'jam' : 'hari' }}" readonly>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Total Harga</label>
                                <input type="text" class="form-control" 
                                       value="Rp {{ number_format($rental->final_amount, 0, ',', '.') }}" readonly>
                            </div>
                        </div>
                    </div>

                    <hr>

                    <!-- Editable Fields -->
                    <div class="row">
                        <div class="col-md-6">
                            <!-- Status -->
                            <div class="mb-3">
                                <label for="status" class="form-label">Status Rental <span class="text-danger">*</span></label>
                                <select name="status" id="status" class="form-select @error('status') is-invalid @enderror" required>
                                    <option value="pending" {{ $rental->status == 'pending' ? 'selected' : '' }}>Pending</option>
                                    <option value="active" {{ $rental->status == 'active' ? 'selected' : '' }}>Aktif</option>
                                    <option value="completed" {{ $rental->status == 'completed' ? 'selected' : '' }}>Selesai</option>
                                    <option value="cancelled" {{ $rental->status == 'cancelled' ? 'selected' : '' }}>Dibatalkan</option>
                                    <option value="overdue" {{ $rental->status == 'overdue' ? 'selected' : '' }}>Terlambat</option>
                                </select>
                                @error('status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                                <div class="form-text">
                                    <small class="text-muted">
                                        <i class="fas fa-info-circle me-1"></i>
                                        Mengubah status akan mempengaruhi ketersediaan PS Unit
                                    </small>
                                </div>
                            </div>
                        </div>

                        <div class="col-md-6">
                            <!-- Payment Status -->
                            <div class="mb-3">
                                <label for="payment_status" class="form-label">Status Pembayaran <span class="text-danger">*</span></label>
                                <select name="payment_status" id="payment_status" class="form-select @error('payment_status') is-invalid @enderror" required>
                                    <option value="unpaid" {{ $rental->payment_status == 'unpaid' ? 'selected' : '' }}>Belum Bayar</option>
                                    <option value="partial" {{ $rental->payment_status == 'partial' ? 'selected' : '' }}>Sebagian</option>
                                    <option value="paid" {{ $rental->payment_status == 'paid' ? 'selected' : '' }}>Lunas</option>
                                    <option value="refunded" {{ $rental->payment_status == 'refunded' ? 'selected' : '' }}>Dikembalikan</option>
                                </select>
                                @error('payment_status')
                                    <div class="invalid-feedback">{{ $message }}</div>
                                @enderror
                            </div>
                        </div>
                    </div>

                    <!-- Notes -->
                    <div class="mb-3">
                        <label for="notes" class="form-label">Catatan</label>
                        <textarea name="notes" id="notes" class="form-control @error('notes') is-invalid @enderror" 
                                  rows="4" placeholder="Catatan tambahan...">{{ old('notes', $rental->notes) }}</textarea>
                        @error('notes')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ route('admin.rentals.show', $rental) }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>
                            Kembali
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            Update Rental
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Current Status -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Status Saat Ini
                </h6>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label class="form-label">Status Rental:</label>
                    @php
                        $statusClass = match($rental->status) {
                            'pending' => 'warning',
                            'active' => 'primary',
                            'completed' => 'success',
                            'cancelled' => 'secondary',
                            'overdue' => 'danger',
                            default => 'secondary'
                        };
                        $statusText = match($rental->status) {
                            'pending' => 'Pending',
                            'active' => 'Aktif',
                            'completed' => 'Selesai',
                            'cancelled' => 'Dibatalkan',
                            'overdue' => 'Terlambat',
                            default => ucfirst($rental->status)
                        };
                    @endphp
                    <div>
                        <span class="badge bg-{{ $statusClass }} fs-6">{{ $statusText }}</span>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">Status Pembayaran:</label>
                    @php
                        $paymentClass = match($rental->payment_status) {
                            'unpaid' => 'danger',
                            'partial' => 'warning',
                            'paid' => 'success',
                            'refunded' => 'info',
                            default => 'secondary'
                        };
                        $paymentText = match($rental->payment_status) {
                            'unpaid' => 'Belum Bayar',
                            'partial' => 'Sebagian',
                            'paid' => 'Lunas',
                            'refunded' => 'Dikembalikan',
                            default => ucfirst($rental->payment_status)
                        };
                    @endphp
                    <div>
                        <span class="badge bg-{{ $paymentClass }} fs-6">{{ $paymentText }}</span>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">Status PS Unit:</label>
                    @php
                        $unitStatusClass = match($rental->psUnit->status) {
                            'available' => 'success',
                            'rented' => 'primary',
                            'maintenance' => 'warning',
                            'broken' => 'danger',
                            default => 'secondary'
                        };
                        $unitStatusText = match($rental->psUnit->status) {
                            'available' => 'Tersedia',
                            'rented' => 'Disewa',
                            'maintenance' => 'Maintenance',
                            'broken' => 'Rusak',
                            default => ucfirst($rental->psUnit->status)
                        };
                    @endphp
                    <div>
                        <span class="badge bg-{{ $unitStatusClass }} fs-6">{{ $unitStatusText }}</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rental Timeline -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-clock me-2"></i>
                    Timeline Rental
                </h6>
            </div>
            <div class="card-body">
                <div class="timeline">
                    <div class="timeline-item">
                        <div class="timeline-marker bg-primary"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">Rental Dibuat</h6>
                            <small class="text-muted">{{ $rental->created_at->format('d/m/Y H:i') }}</small>
                        </div>
                    </div>

                    <div class="timeline-item">
                        <div class="timeline-marker bg-info"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">Waktu Mulai</h6>
                            <small class="text-muted">{{ $rental->start_datetime->format('d/m/Y H:i') }}</small>
                        </div>
                    </div>

                    <div class="timeline-item">
                        <div class="timeline-marker bg-warning"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">Waktu Selesai</h6>
                            <small class="text-muted">{{ $rental->end_datetime->format('d/m/Y H:i') }}</small>
                        </div>
                    </div>

                    @if($rental->actual_return_datetime)
                    <div class="timeline-item">
                        <div class="timeline-marker bg-success"></div>
                        <div class="timeline-content">
                            <h6 class="mb-1">Waktu Kembali</h6>
                            <small class="text-muted">{{ $rental->actual_return_datetime->format('d/m/Y H:i') }}</small>
                            @if($rental->actual_return_datetime > $rental->end_datetime)
                                <br><small class="text-danger">Terlambat</small>
                            @endif
                        </div>
                    </div>
                    @endif
                </div>
            </div>
        </div>

        <!-- Status Change Guide -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-question-circle me-2"></i>
                    Panduan Status
                </h6>
            </div>
            <div class="card-body">
                <div class="small">
                    <div class="mb-2">
                        <span class="badge bg-warning">Pending</span>
                        <span class="ms-2">Rental baru, belum dimulai</span>
                    </div>
                    <div class="mb-2">
                        <span class="badge bg-primary">Aktif</span>
                        <span class="ms-2">Rental sedang berjalan</span>
                    </div>
                    <div class="mb-2">
                        <span class="badge bg-success">Selesai</span>
                        <span class="ms-2">PS Unit sudah dikembalikan</span>
                    </div>
                    <div class="mb-2">
                        <span class="badge bg-danger">Terlambat</span>
                        <span class="ms-2">Melewati batas waktu</span>
                    </div>
                    <div class="mb-0">
                        <span class="badge bg-secondary">Dibatalkan</span>
                        <span class="ms-2">Rental dibatalkan</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
}

.timeline-content {
    padding-left: 10px;
}
</style>
@endsection
