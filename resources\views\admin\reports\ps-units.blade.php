@extends('layouts.app')

@section('title', 'Laporan PS Units - Rental PS Admin')
@section('page-title', 'Laporan PS Units')

@section('content')
<div class="row mb-4">
    <div class="col-md-6">
        <h4>
            <i class="fas fa-gamepad me-2"></i>
            Laporan PS Units
        </h4>
    </div>
    <div class="col-md-6 text-end">
        <a href="{{ route('admin.reports.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>
            Kembali ke Dashboard
        </a>
    </div>
</div>

<!-- Statistik PS Units -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card text-center" style="background: white; border: 1px solid #e5e7eb;">
            <div class="card-body">
                <i class="fas fa-gamepad fa-2x mb-2" style="color: #667eea;"></i>
                <h4 class="text-dark">{{ number_format($stats['total_units']) }}</h4>
                <small class="text-muted">Total PS Units</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card text-center" style="background: white; border: 1px solid #e5e7eb;">
            <div class="card-body">
                <i class="fas fa-check-circle fa-2x mb-2" style="color: #11998e;"></i>
                <h4 class="text-dark">{{ number_format($stats['available_units']) }}</h4>
                <small class="text-muted">Tersedia</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card text-center" style="background: white; border: 1px solid #e5e7eb;">
            <div class="card-body">
                <i class="fas fa-clock fa-2x mb-2" style="color: #ff9a9e;"></i>
                <h4 class="text-dark">{{ number_format($stats['rented_units']) }}</h4>
                <small class="text-muted">Sedang Disewa</small>
            </div>
        </div>
    </div>

</div>

<!-- Filter -->
<div class="card mb-4">
    <div class="card-header">
        <h6 class="mb-0">
            <i class="fas fa-filter me-2"></i>
            Filter PS Units
        </h6>
    </div>
    <div class="card-body">
        <form method="GET" action="{{ route('admin.reports.ps-units') }}">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label class="form-label">Status Unit</label>
                    <select name="status" class="form-select">
                        <option value="">Semua Status</option>
                        <option value="available" {{ request('status') == 'available' ? 'selected' : '' }}>Tersedia</option>
                        <option value="rented" {{ request('status') == 'rented' ? 'selected' : '' }}>Sedang Disewa</option>
                        <option value="maintenance" {{ request('status') == 'maintenance' ? 'selected' : '' }}>Maintenance</option>
                        <option value="broken" {{ request('status') == 'broken' ? 'selected' : '' }}>Rusak</option>
                    </select>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Tipe PS</label>
                    <input type="text" name="ps_type" class="form-control" 
                           placeholder="Cari tipe PS..." value="{{ request('ps_type') }}">
                </div>
                <div class="col-md-4 mb-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i>
                        Filter
                    </button>
                    <a href="{{ route('admin.reports.ps-units') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        Reset
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Tabel PS Units -->
<div class="card">
    <div class="card-header">
        <h6 class="mb-0">
            <i class="fas fa-table me-2"></i>
            Performance PS Units ({{ $psUnits->total() }} total)
        </h6>
    </div>
    <div class="card-body">
        @if($psUnits->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Image</th>
                            <th>Unit Code</th>
                            <th>Tipe & Brand</th>
                            <th>Harga</th>
                            <th>Total Rental</th>
                            <th>Total Revenue</th>
                            <th>Status</th>
                            <th>Kondisi</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($psUnits as $unit)
                        <tr>
                            <td>
                                <div class="text-center">
                                    <img src="{{ $unit->ps_image }}"
                                         alt="{{ $unit->ps_display_name }}"
                                         class="img-thumbnail"
                                         style="width: 50px; height: 50px; object-fit: contain;"
                                         onerror="this.src='{{ asset('images/ps-units/placeholder.svg') }}'">
                                </div>
                            </td>
                            <td>
                                <strong class="text-primary">{{ $unit->unit_code }}</strong>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ $unit->ps_display_name }}</strong><br>
                                    <small class="text-muted">{{ $unit->brand }}</small>
                                </div>
                            </td>
                            <td>
                                <div>
                                    <small class="text-muted">Per Jam:</small> Rp {{ number_format($unit->price_per_hour, 0, ',', '.') }}<br>
                                    <small class="text-muted">Per Hari:</small> Rp {{ number_format($unit->price_per_day, 0, ',', '.') }}
                                </div>
                            </td>
                            <td>
                                <div class="text-center">
                                    <span class="badge bg-info fs-6">{{ $unit->rentals_count }}</span>
                                    @if($unit->rentals_count > 0)
                                        <br><small class="text-muted">rental</small>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div class="text-end">
                                    @if($unit->total_revenue > 0)
                                        <strong class="text-success">Rp {{ number_format($unit->total_revenue, 0, ',', '.') }}</strong>
                                    @else
                                        <span class="text-muted">Rp 0</span>
                                    @endif
                                </div>
                            </td>
                            <td>
                                @php
                                    $statusClass = match($unit->status) {
                                        'available' => 'success',
                                        'rented' => 'primary',
                                        'maintenance' => 'warning',
                                        'broken' => 'danger',
                                        default => 'secondary'
                                    };
                                    $statusText = match($unit->status) {
                                        'available' => 'Tersedia',
                                        'rented' => 'Disewa',
                                        'maintenance' => 'Maintenance',
                                        'broken' => 'Rusak',
                                        default => ucfirst($unit->status)
                                    };
                                @endphp
                                <span class="badge bg-{{ $statusClass }}">{{ $statusText }}</span>
                            </td>
                            <td>
                                @php
                                    $conditionClass = match($unit->condition) {
                                        'excellent' => 'success',
                                        'good' => 'primary',
                                        'fair' => 'warning',
                                        'poor' => 'danger',
                                        default => 'secondary'
                                    };
                                @endphp
                                <span class="badge bg-{{ $conditionClass }}">{{ ucfirst($unit->condition) }}</span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.ps-units.show', $unit) }}" 
                                       class="btn btn-sm btn-outline-info" 
                                       title="Lihat Detail">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    @if($unit->rentals_count > 0)
                                        <a href="{{ route('admin.rentals.index', ['ps_unit_id' => $unit->id]) }}" 
                                           class="btn btn-sm btn-outline-primary" 
                                           title="Lihat Rental">
                                            <i class="fas fa-calendar-alt"></i>
                                        </a>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $psUnits->appends(request()->query())->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-gamepad fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Tidak ada data PS Unit</h5>
                <p class="text-muted">Tidak ada PS Unit yang sesuai dengan filter yang dipilih.</p>
                <a href="{{ route('admin.reports.ps-units') }}" class="btn btn-primary">
                    <i class="fas fa-refresh me-1"></i>
                    Reset Filter
                </a>
            </div>
        @endif
    </div>
</div>

<!-- Performance Analysis -->
@if($psUnits->count() > 0)
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-trophy me-2"></i>
                    Top 5 PS Unit Berdasarkan Revenue
                </h6>
            </div>
            <div class="card-body">
                @php
                    $topRevenue = $psUnits->sortByDesc('total_revenue')->take(5);
                @endphp
                @foreach($topRevenue as $index => $unit)
                    @if($unit->total_revenue > 0)
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div class="d-flex align-items-center">
                                <span class="badge bg-success me-2">{{ $index + 1 }}</span>
                                <div>
                                    <strong>{{ $unit->unit_code }}</strong><br>
                                    <small class="text-muted">{{ $unit->ps_type }}</small>
                                </div>
                            </div>
                            <div class="text-end">
                                <strong class="text-success">Rp {{ number_format($unit->total_revenue, 0, ',', '.') }}</strong><br>
                                <small class="text-muted">{{ $unit->rentals_count }} rental</small>
                            </div>
                        </div>
                        @if(!$loop->last)<hr>@endif
                    @endif
                @endforeach
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    Top 5 PS Unit Berdasarkan Frekuensi
                </h6>
            </div>
            <div class="card-body">
                @php
                    $topFrequent = $psUnits->sortByDesc('rentals_count')->take(5);
                @endphp
                @foreach($topFrequent as $index => $unit)
                    @if($unit->rentals_count > 0)
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div class="d-flex align-items-center">
                                <span class="badge bg-primary me-2">{{ $index + 1 }}</span>
                                <div>
                                    <strong>{{ $unit->unit_code }}</strong><br>
                                    <small class="text-muted">{{ $unit->ps_type }}</small>
                                </div>
                            </div>
                            <div class="text-end">
                                <strong class="text-primary">{{ $unit->rentals_count }} rental</strong><br>
                                <small class="text-muted">
                                    @if($unit->total_revenue > 0)
                                        Rp {{ number_format($unit->total_revenue, 0, ',', '.') }}
                                    @else
                                        Rp 0
                                    @endif
                                </small>
                            </div>
                        </div>
                        @if(!$loop->last)<hr>@endif
                    @endif
                @endforeach
            </div>
        </div>
    </div>
</div>

<!-- Unit Status Distribution -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    Distribusi Status PS Units
                </h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        <div class="border rounded p-3 mb-3" style="border-color: #198754 !important;">
                            <h4 class="text-success">{{ $stats['available_units'] }}</h4>
                            <small class="text-muted">Tersedia</small>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="border rounded p-3 mb-3" style="border-color: #0d6efd !important;">
                            <h4 class="text-primary">{{ $stats['rented_units'] }}</h4>
                            <small class="text-muted">Sedang Disewa</small>
                        </div>
                    </div>

                    <div class="col-md-3 text-center">
                        <div class="border rounded p-3 mb-3" style="border-color: #dc3545 !important;">
                            <h4 class="text-danger">{{ $psUnits->where('status', 'broken')->count() }}</h4>
                            <small class="text-muted">Rusak</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endif
@endsection
