<?php

namespace Tests\Feature;

use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use Tests\TestCase;
use App\Models\User;
use App\Models\PsUnit;
use App\Models\Customer;

class DashboardTest extends TestCase
{
    use RefreshDatabase;

    public function test_dashboard_loads_successfully(): void
    {
        // Create admin user
        $admin = User::factory()->create([
            'email' => '<EMAIL>',
            'role' => 'admin',
            'status' => 'active'
        ]);

        // Create some test data
        PsUnit::factory()->count(3)->create();
        Customer::factory()->count(2)->create();

        // Test dashboard access
        $response = $this->actingAs($admin)->get('/admin/dashboard');

        $response->assertStatus(200);
        $response->assertViewIs('admin.dashboard');
        $response->assertViewHas('stats');
    }

    public function test_models_work_correctly(): void
    {
        // Test PsUnit model
        $psUnit = PsUnit::factory()->create(['status' => 'available']);
        $this->assertEquals('available', $psUnit->getAttribute('status'));
        $this->assertTrue($psUnit->isAvailable());

        // Test Customer model
        $customer = Customer::factory()->create(['status' => 'active']);
        $this->assertEquals('active', $customer->getAttribute('status'));
        $this->assertTrue($customer->isActive());
    }
}
