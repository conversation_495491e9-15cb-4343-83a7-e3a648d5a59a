<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('customers', function (Blueprint $table) {
            $table->id();
            $table->string('name'); // Nama customer
            $table->string('email')->unique(); // Email customer
            $table->timestamp('email_verified_at')->nullable();
            $table->string('password'); // Password untuk login Android
            $table->string('phone')->nullable(); // Nomor telepon
            $table->text('address')->nullable(); // Alamat customer
            $table->date('birth_date')->nullable(); // Tanggal lahir
            $table->enum('gender', ['male', 'female'])->nullable(); // Jenis kelamin
            $table->string('id_card_number')->nullable(); // Nomor KTP

            $table->rememberToken();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('customers');
    }
};
