<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Rental;
use App\Models\PsUnit;
use Illuminate\Http\JsonResponse;
use Carbon\Carbon;

class RentalController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        $customer = $request->user();

        $rentals = Rental::where('customer_id', $customer->getKey())
                        ->with(['psUnit'])
                        ->orderBy('created_at', 'desc')
                        ->paginate(10);

        return response()->json([
            'success' => true,
            'message' => 'Rental history retrieved successfully',
            'data' => $rentals
        ]);
    }

    public function store(Request $request): JsonResponse
    {
        $customer = $request->user();

        $request->validate([
            'ps_unit_id' => 'required|exists:ps_units,id',
            'rental_type' => 'required|in:hourly,daily',
            'duration' => 'required|integer|min:1',
            'start_datetime' => 'required|date|after:now',
        ]);

        $psUnit = PsUnit::find($request->get('ps_unit_id'));

        if (!$psUnit || $psUnit->getAttribute('status') !== 'available') {
            return response()->json([
                'success' => false,
                'message' => 'PS Unit is not available for rental'
            ], 400);
        }

        // Convert duration to integer
        $duration = (int) $request->get('duration');

        // Calculate pricing
        $pricePerUnit = $request->get('rental_type') === 'hourly'
                       ? $psUnit->getAttribute('price_per_hour')
                       : $psUnit->getAttribute('price_per_day');

        $totalPrice = $pricePerUnit * $duration;
        $deposit = $totalPrice * 0.2; // 20% deposit

        // Calculate end datetime
        $startDatetime = Carbon::parse($request->get('start_datetime'));
        $endDatetime = $request->get('rental_type') === 'hourly'
                      ? $startDatetime->copy()->addHours($duration)
                      : $startDatetime->copy()->addDays($duration);

        // Generate rental code
        $rentalCode = 'RNT' . str_pad(Rental::count() + 1, 4, '0', STR_PAD_LEFT);

        $rental = Rental::create([
            'rental_code' => $rentalCode,
            'customer_id' => $customer->getKey(),
            'ps_unit_id' => $request->get('ps_unit_id'),
            'start_datetime' => $startDatetime,
            'end_datetime' => $endDatetime,
            'rental_type' => $request->get('rental_type'),
            'duration' => $duration, // Use converted integer
            'price_per_unit' => $pricePerUnit,
            'total_price' => $totalPrice,
            'deposit' => $deposit,
            'final_amount' => $totalPrice,
            'status' => 'pending',
            'payment_status' => 'unpaid',
            'notes' => $request->get('notes'),
        ]);

        // Update PS Unit status
        $psUnit->update(['status' => 'rented']);

        return response()->json([
            'success' => true,
            'message' => 'Rental created successfully',
            'data' => $rental->load('psUnit')
        ], 201);
    }

    public function show(Request $request, int $id): JsonResponse
    {
        $customer = $request->user();

        $rental = Rental::where('id', $id)
                       ->where('customer_id', $customer->getKey())
                       ->with(['psUnit'])
                       ->first();

        if (!$rental) {
            return response()->json([
                'success' => false,
                'message' => 'Rental not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'message' => 'Rental retrieved successfully',
            'data' => $rental
        ]);
    }

    public function cancel(Request $request, int $id): JsonResponse
    {
        $customer = $request->user();

        $rental = Rental::where('id', $id)
                       ->where('customer_id', $customer->getKey())
                       ->first();

        if (!$rental) {
            return response()->json([
                'success' => false,
                'message' => 'Rental not found'
            ], 404);
        }

        if ($rental->getAttribute('status') !== 'pending') {
            return response()->json([
                'success' => false,
                'message' => 'Only pending rentals can be cancelled'
            ], 400);
        }

        // Update rental status
        $rental->update(['status' => 'cancelled']);

        // Update PS Unit status back to available
        $psUnit = PsUnit::find($rental->getAttribute('ps_unit_id'));
        if ($psUnit) {
            $psUnit->update(['status' => 'available']);
        }

        return response()->json([
            'success' => true,
            'message' => 'Rental cancelled successfully',
            'data' => $rental
        ]);
    }
}
