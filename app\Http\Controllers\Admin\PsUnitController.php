<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\PsUnit;

class PsUnitController extends Controller
{
    // Middleware handled in routes

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $psUnits = PsUnit::orderBy('created_at', 'desc')->paginate(10);
        return view('admin.ps-units.index', compact('psUnits'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('admin.ps-units.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'unit_code' => 'required|unique:ps_units',
            'ps_type' => 'required',
            'brand' => 'required',
            'price_per_hour' => 'required|numeric|min:0',
            'price_per_day' => 'required|numeric|min:0',
            'status' => 'required|in:available,rented,maintenance,broken',
            'condition' => 'required',
            'custom_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'purchase_date' => 'nullable|date',
            'total_stock' => 'required|integer|min:1',
            'available_stock' => 'required|integer|min:0|lte:total_stock',
        ]);

        $data = $request->except(['custom_image']);

        // Handle accessories as array
        if ($request->has('accessories')) {
            $data['accessories'] = array_filter(explode(',', $request->get('accessories')));
        }

        $psUnit = PsUnit::create($data);

        // Handle custom image upload after creation
        if ($request->hasFile('custom_image')) {
            $imagePath = $request->file('custom_image')->store('ps-units', 'public');
            try {
                \DB::table('ps_units')->where('id', $psUnit->id)->update(['custom_image' => $imagePath]);
            } catch (\Exception $e) {
                // If column doesn't exist, ignore for now
                \Log::warning('Custom image column not found: ' . $e->getMessage());
            }
        }

        return redirect()->route('admin.ps-units.index')
                        ->with('success', 'PS Unit berhasil ditambahkan.');
    }

    /**
     * Display the specified resource.
     */
    public function show(PsUnit $psUnit)
    {
        return view('admin.ps-units.show', compact('psUnit'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(PsUnit $psUnit)
    {
        return view('admin.ps-units.edit', compact('psUnit'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, PsUnit $psUnit)
    {
        $request->validate([
            'unit_code' => 'required|unique:ps_units,unit_code,' . $psUnit->getKey(),
            'ps_type' => 'required',
            'brand' => 'required',
            'price_per_hour' => 'required|numeric|min:0',
            'price_per_day' => 'required|numeric|min:0',
            'status' => 'required|in:available,rented,maintenance,broken',
            'condition' => 'required',
            'custom_image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'purchase_date' => 'nullable|date',
            'total_stock' => 'required|integer|min:1',
            'available_stock' => 'required|integer|min:0|lte:total_stock',
        ]);

        $data = $request->except(['custom_image']);

        // Handle accessories as array
        if ($request->has('accessories')) {
            $data['accessories'] = array_filter(explode(',', $request->get('accessories')));
        }

        $psUnit->update($data);

        // Handle custom image upload after update
        if ($request->hasFile('custom_image')) {
            try {
                // Get current custom image
                $currentImage = \DB::table('ps_units')->where('id', $psUnit->id)->value('custom_image');

                // Delete old custom image if exists
                if ($currentImage && \Storage::disk('public')->exists($currentImage)) {
                    \Storage::disk('public')->delete($currentImage);
                }

                $imagePath = $request->file('custom_image')->store('ps-units', 'public');
                \DB::table('ps_units')->where('id', $psUnit->id)->update(['custom_image' => $imagePath]);
            } catch (\Exception $e) {
                // If column doesn't exist, ignore for now
                \Log::warning('Custom image column not found: ' . $e->getMessage());
            }
        }

        return redirect()->route('admin.ps-units.index')
                        ->with('success', 'PS Unit berhasil diupdate.');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(PsUnit $psUnit)
    {
        // Check if unit is currently rented
        if ($psUnit->getAttribute('status') === 'rented') {
            return redirect()->route('admin.ps-units.index')
                            ->with('error', 'Tidak dapat menghapus PS Unit yang sedang disewa.');
        }

        $psUnit->delete();

        return redirect()->route('admin.ps-units.index')
                        ->with('success', 'PS Unit berhasil dihapus.');
    }
}
