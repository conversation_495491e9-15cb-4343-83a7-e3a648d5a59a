@extends('layouts.app')

@section('title', 'Buat Rental Baru - Rental PS Admin')
@section('page-title', 'Buat Rental Baru')

@section('content')
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-plus me-2"></i>
                    Form Rental Baru
                </h5>
            </div>
            <div class="card-body">
                <!-- Error Messages -->
                @if($errors->any())
                    <div class="alert alert-danger">
                        <h6><i class="fas fa-exclamation-triangle me-2"></i>Ter<PERSON><PERSON>:</h6>
                        <ul class="mb-0">
                            @foreach($errors->all() as $error)
                                <li>{{ $error }}</li>
                            @endforeach
                        </ul>
                    </div>
                @endif

                <!-- Warning Messages -->
                @if(session('warning'))
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>{{ session('warning') }}
                    </div>
                @endif

                <form method="POST" action="{{ route('admin.rentals.store') }}">
                    @csrf
                    
                    <!-- Customer Selection -->
                    <div class="mb-3">
                        <label for="customer_id" class="form-label">Customer <span class="text-danger">*</span></label>
                        <select name="customer_id" id="customer_id" class="form-select @error('customer_id') is-invalid @enderror" required>
                            <option value="">Pilih Customer</option>
                            @forelse($customers as $customer)
                                <option value="{{ $customer->id }}"
                                        {{ (old('customer_id', $selectedCustomerId ?? '') == $customer->id) ? 'selected' : '' }}>
                                    {{ $customer->name }} - {{ $customer->phone }}
                                </option>
                            @empty
                                <option value="" disabled>Tidak ada customer</option>
                            @endforelse
                        </select>
                        @error('customer_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        @if($customers->isEmpty())
                            <div class="form-text text-warning">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                Tidak ada customer. <a href="{{ route('admin.customers.create') }}">Tambah customer baru</a>
                            </div>
                        @endif
                    </div>

                    <!-- PS Unit Selection -->
                    <div class="mb-3">
                        <label for="ps_unit_id" class="form-label">PS Unit <span class="text-danger">*</span></label>
                        <select name="ps_unit_id" id="ps_unit_id" class="form-select @error('ps_unit_id') is-invalid @enderror" required>
                            <option value="">Pilih PS Unit</option>
                            @forelse($psUnits as $unit)
                                <option value="{{ $unit->id }}"
                                        data-price-hour="{{ $unit->price_per_hour }}"
                                        data-price-day="{{ $unit->price_per_day }}"
                                        {{ old('ps_unit_id') == $unit->id ? 'selected' : '' }}>
                                    {{ $unit->unit_code }} - {{ $unit->ps_type }}
                                    (Rp {{ number_format($unit->price_per_hour, 0, ',', '.') }}/jam,
                                     Rp {{ number_format($unit->price_per_day, 0, ',', '.') }}/hari)
                                </option>
                            @empty
                                <option value="" disabled>Tidak ada PS Unit tersedia</option>
                            @endforelse
                        </select>
                        @error('ps_unit_id')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                        @if($psUnits->isEmpty())
                            <div class="form-text text-warning">
                                <i class="fas fa-exclamation-triangle me-1"></i>
                                Tidak ada PS Unit tersedia. <a href="{{ route('admin.ps-units.create') }}">Tambah PS Unit baru</a>
                            </div>
                        @endif
                    </div>

                    <div class="row">
                        <!-- Rental Type -->
                        <div class="col-md-6 mb-3">
                            <label for="rental_type" class="form-label">Tipe Rental <span class="text-danger">*</span></label>
                            <select name="rental_type" id="rental_type" class="form-select @error('rental_type') is-invalid @enderror" required>
                                <option value="">Pilih Tipe</option>
                                <option value="hourly" {{ old('rental_type') == 'hourly' ? 'selected' : '' }}>Per Jam</option>
                                <option value="daily" {{ old('rental_type') == 'daily' ? 'selected' : '' }}>Per Hari</option>
                            </select>
                            @error('rental_type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <!-- Duration -->
                        <div class="col-md-6 mb-3">
                            <label for="duration" class="form-label">Durasi <span class="text-danger">*</span></label>
                            <input type="number" name="duration" id="duration"
                                   class="form-control @error('duration') is-invalid @enderror"
                                   value="{{ old('duration') }}" min="1" required>
                            <div class="form-text" id="duration-help" style="display: none;"></div>
                            @error('duration')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- Start DateTime -->
                    <div class="mb-3">
                        <label for="start_datetime" class="form-label">Waktu Mulai <span class="text-danger">*</span></label>
                        <input type="datetime-local" name="start_datetime" id="start_datetime" 
                               class="form-control @error('start_datetime') is-invalid @enderror" 
                               value="{{ old('start_datetime') }}" required>
                        @error('start_datetime')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <!-- Notes -->
                    <div class="mb-3">
                        <label for="notes" class="form-label">Catatan</label>
                        <textarea name="notes" id="notes" class="form-control @error('notes') is-invalid @enderror" 
                                  rows="3" placeholder="Catatan tambahan (opsional)">{{ old('notes') }}</textarea>
                        @error('notes')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="{{ route('admin.rentals.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>
                            Kembali
                        </a>
                        <button type="submit" class="btn btn-primary" id="submitBtn">
                            <i class="fas fa-save me-1"></i>
                            Simpan Rental
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Price Calculator -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-calculator me-2"></i>
                    Kalkulator Harga
                </h6>
            </div>
            <div class="card-body">
                <div id="price-calculator" style="display: none;">
                    <div class="mb-3">
                        <label class="form-label">Harga per Unit:</label>
                        <div id="price-per-unit" class="fw-bold text-primary">-</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Total Harga:</label>
                        <div id="total-price" class="fw-bold text-success">-</div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Deposit (20%):</label>
                        <div id="deposit" class="fw-bold text-warning">-</div>
                    </div>
                    <hr>
                    <div class="mb-0">
                        <label class="form-label">Waktu Selesai:</label>
                        <div id="end-time" class="fw-bold text-info">-</div>
                    </div>
                </div>
                <div id="calculator-placeholder" class="text-center text-muted">
                    <i class="fas fa-info-circle fa-2x mb-2"></i>
                    <p>Pilih PS Unit, tipe rental, dan durasi untuk melihat kalkulasi harga</p>
                </div>
            </div>
        </div>

        <!-- Available PS Units -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-gamepad me-2"></i>
                    PS Unit Tersedia ({{ $psUnits->count() }})
                </h6>
            </div>
            <div class="card-body">
                @if($psUnits->count() > 0)
                    @foreach($psUnits as $unit)
                        <div class="border rounded p-2 mb-2">
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <strong>{{ $unit->unit_code }}</strong><br>
                                    <small class="text-muted">{{ $unit->ps_type }}</small>
                                </div>
                                <div class="text-end">
                                    <small class="text-success">Tersedia</small>
                                </div>
                            </div>
                            <div class="mt-1">
                                <small class="text-muted">
                                    Rp {{ number_format($unit->price_per_hour, 0, ',', '.') }}/jam<br>
                                    Rp {{ number_format($unit->price_per_day, 0, ',', '.') }}/hari
                                </small>
                            </div>
                        </div>
                    @endforeach
                @else
                    <div class="text-center text-muted">
                        <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                        <p>Tidak ada PS Unit yang tersedia</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const psUnitSelect = document.getElementById('ps_unit_id');
    const rentalTypeSelect = document.getElementById('rental_type');
    const durationInput = document.getElementById('duration');
    const startDatetimeInput = document.getElementById('start_datetime');
    
    function updateCalculator() {
        const selectedUnit = psUnitSelect.options[psUnitSelect.selectedIndex];
        const rentalType = rentalTypeSelect.value;
        const duration = parseInt(durationInput.value) || 0;
        const startDatetime = startDatetimeInput.value;

        // Debug: log values to console
        console.log('Calculator Update:', {
            unitSelected: !!selectedUnit.value,
            rentalType: rentalType,
            duration: duration,
            startDatetime: startDatetime,
            priceHour: selectedUnit.dataset?.priceHour,
            priceDay: selectedUnit.dataset?.priceDay
        });

        if (selectedUnit.value && rentalType && duration > 0) {
            const pricePerHour = parseFloat(selectedUnit.dataset.priceHour) || 0;
            const pricePerDay = parseFloat(selectedUnit.dataset.priceDay) || 0;
            const pricePerUnit = rentalType === 'hourly' ? pricePerHour : pricePerDay;
            const totalPrice = pricePerUnit * duration;
            const deposit = totalPrice * 0.2;

            // Update price display
            document.getElementById('price-per-unit').textContent =
                'Rp ' + pricePerUnit.toLocaleString('id-ID');
            document.getElementById('total-price').textContent =
                'Rp ' + totalPrice.toLocaleString('id-ID');
            document.getElementById('deposit').textContent =
                'Rp ' + deposit.toLocaleString('id-ID');

            // Calculate and display end time
            if (startDatetime) {
                const startDate = new Date(startDatetime);
                const endDate = new Date(startDate);
                if (rentalType === 'hourly') {
                    endDate.setHours(endDate.getHours() + duration);
                } else {
                    endDate.setDate(endDate.getDate() + duration);
                }

                // Format end time nicely
                const options = {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    hour12: false
                };
                document.getElementById('end-time').textContent =
                    endDate.toLocaleDateString('id-ID', options);
            } else {
                document.getElementById('end-time').textContent = 'Pilih waktu mulai';
            }

            // Show calculator
            document.getElementById('price-calculator').style.display = 'block';
            document.getElementById('calculator-placeholder').style.display = 'none';
        } else {
            // Hide calculator and show placeholder
            document.getElementById('price-calculator').style.display = 'none';
            document.getElementById('calculator-placeholder').style.display = 'block';

            // Update placeholder message based on what's missing
            const placeholder = document.getElementById('calculator-placeholder');
            let missingItems = [];
            if (!selectedUnit.value) missingItems.push('PS Unit');
            if (!rentalType) missingItems.push('Tipe Rental');
            if (duration <= 0) missingItems.push('Durasi');

            if (missingItems.length > 0) {
                placeholder.innerHTML = `
                    <i class="fas fa-info-circle fa-2x mb-2"></i>
                    <p>Lengkapi data berikut untuk melihat kalkulasi harga:</p>
                    <ul class="list-unstyled">
                        ${missingItems.map(item => `<li>• ${item}</li>`).join('')}
                    </ul>
                `;
            }
        }
    }
    
    // Function to update duration constraints based on rental type and start datetime
    function updateDurationConstraints() {
        const rentalType = rentalTypeSelect.value;
        const startDatetime = startDatetimeInput.value;
        const durationHelp = document.getElementById('duration-help');

        if (rentalType === 'hourly' && startDatetime) {
            const startDate = new Date(startDatetime);
            const endOfDay = new Date(startDate);
            endOfDay.setHours(23, 59, 59, 999);

            // Calculate maximum hours available until end of day
            const maxHours = Math.floor((endOfDay - startDate) / (1000 * 60 * 60)) + 1;

            durationInput.max = Math.max(1, maxHours);
            durationInput.setAttribute('title', `Maksimal ${maxHours} jam untuk hari ini`);

            // Show help text
            durationHelp.textContent = `Maksimal ${maxHours} jam tersedia untuk hari yang dipilih`;
            durationHelp.style.display = 'block';
            durationHelp.className = 'form-text text-info';

            // If current duration exceeds max, reset it
            if (parseInt(durationInput.value) > maxHours) {
                durationInput.value = maxHours;
            }
        } else if (rentalType === 'daily') {
            durationInput.removeAttribute('max');
            durationInput.setAttribute('title', 'Durasi dalam hari');

            // Show help text for daily
            durationHelp.textContent = 'Durasi dalam hari (tidak ada batasan maksimal)';
            durationHelp.style.display = 'block';
            durationHelp.className = 'form-text text-muted';
        } else {
            durationInput.removeAttribute('max');
            durationInput.removeAttribute('title');
            durationHelp.style.display = 'none';
        }

        updateCalculator();
    }

    // Event listeners
    psUnitSelect.addEventListener('change', function() {
        console.log('PS Unit changed:', this.value);
        updateCalculator();
    });

    rentalTypeSelect.addEventListener('change', function() {
        console.log('Rental type changed:', this.value);
        updateDurationConstraints();
        updateCalculator();
    });

    durationInput.addEventListener('input', function() {
        console.log('Duration changed:', this.value);
        updateCalculator();
    });

    startDatetimeInput.addEventListener('change', function() {
        console.log('Start datetime changed:', this.value);
        updateDurationConstraints();
        updateCalculator();
    });

    // Initial update on page load
    updateCalculator();

    // Set minimum datetime to now
    const now = new Date();
    now.setMinutes(now.getMinutes() - now.getTimezoneOffset());
    startDatetimeInput.min = now.toISOString().slice(0, 16);

    // Set default start datetime to current time if not set
    if (!startDatetimeInput.value) {
        startDatetimeInput.value = now.toISOString().slice(0, 16);
    }

    // Form validation
    const form = document.querySelector('form');
    const submitBtn = document.getElementById('submitBtn');

    form.addEventListener('submit', function(e) {
        let isValid = true;
        let errorMessages = [];

        // Check customer selection
        if (!psUnitSelect.value) {
            isValid = false;
            errorMessages.push('PS Unit harus dipilih');
        }

        // Check customer selection
        const customerSelect = document.getElementById('customer_id');
        if (!customerSelect.value) {
            isValid = false;
            errorMessages.push('Customer harus dipilih');
        }

        // Check rental type
        if (!rentalTypeSelect.value) {
            isValid = false;
            errorMessages.push('Tipe rental harus dipilih');
        }

        // Check duration
        if (!durationInput.value || durationInput.value < 1) {
            isValid = false;
            errorMessages.push('Durasi harus diisi dan minimal 1');
        }

        // Additional validation for hourly rentals
        if (rentalTypeSelect.value === 'hourly' && startDatetimeInput.value && durationInput.value) {
            const startDate = new Date(startDatetimeInput.value);
            const endOfDay = new Date(startDate);
            endOfDay.setHours(23, 59, 59, 999);

            const maxHours = Math.floor((endOfDay - startDate) / (1000 * 60 * 60)) + 1;
            const requestedDuration = parseInt(durationInput.value);

            if (requestedDuration > maxHours) {
                isValid = false;
                errorMessages.push(`Durasi maksimal untuk rental per jam adalah ${maxHours} jam pada hari yang dipilih`);
            }
        }

        // Check start datetime
        if (!startDatetimeInput.value) {
            isValid = false;
            errorMessages.push('Waktu mulai harus diisi');
        }

        if (!isValid) {
            e.preventDefault();
            alert('Mohon lengkapi data berikut:\n- ' + errorMessages.join('\n- '));
            return false;
        }

        // Disable submit button to prevent double submission
        submitBtn.disabled = true;
        submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Menyimpan...';
    });
});
</script>
@endsection
