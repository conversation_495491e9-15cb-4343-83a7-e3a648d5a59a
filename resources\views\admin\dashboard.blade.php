@extends('layouts.app')

@section('title', 'Dashboard - Rental PS Admin')
@section('page-title', 'Dashboard')

@section('content')
<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card stats-card">
            <div class="card-body text-center">
                <i class="fas fa-gamepad fa-2x mb-3"></i>
                <h3 class="mb-1">{{ $stats['total_ps_units'] }}</h3>
                <p class="mb-0">Total PS Units</p>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-check-circle fa-2x mb-3 text-success"></i>
                <h3 class="mb-1">{{ $stats['available_units'] }}</h3>
                <p class="mb-0">Available Units</p>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-clock fa-2x mb-3 text-warning"></i>
                <h3 class="mb-1">{{ $stats['rented_units'] }}</h3>
                <p class="mb-0">Rented Units</p>
            </div>
        </div>
    </div>

    <div class="col-md-3 mb-3">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-users fa-2x mb-3 text-info"></i>
                <h3 class="mb-1">{{ $stats['total_customers'] }}</h3>
                <p class="mb-0">Total Customers</p>
            </div>
        </div>
    </div>
</div>

<!-- Revenue Cards -->
<div class="row mb-4">
    <div class="col-md-6 mb-3">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-money-bill-wave fa-2x mb-3 text-primary"></i>
                <h4 class="mb-1">Rp {{ number_format($stats['today_revenue'], 0, ',', '.') }}</h4>
                <p class="mb-0">Today's Revenue</p>
            </div>
        </div>
    </div>

    <div class="col-md-6 mb-3">
        <div class="card">
            <div class="card-body text-center">
                <i class="fas fa-chart-line fa-2x mb-3 text-primary"></i>
                <h4 class="mb-1">Rp {{ number_format($stats['month_revenue'], 0, ',', '.') }}</h4>
                <p class="mb-0">This Month's Revenue</p>
            </div>
        </div>
    </div>
</div>

<!-- Status Cards -->
<div class="row mb-4">
    <div class="col-md-4 mb-3">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h6 class="mb-0">
                    <i class="fas fa-calendar-check me-2"></i>
                    Rental Status
                </h6>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>Active</span>
                    <span class="badge bg-success">{{ $stats['active_rentals'] }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>Pending</span>
                    <span class="badge bg-warning">{{ $stats['pending_rentals'] }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>Overdue</span>
                    <span class="badge bg-danger">{{ $stats['overdue_rentals'] }}</span>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4 mb-3">
        <div class="card">
            <div class="card-header bg-info text-white">
                <h6 class="mb-0">
                    <i class="fas fa-gamepad me-2"></i>
                    Unit Status
                </h6>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>Available</span>
                    <span class="badge bg-success">{{ $stats['available_units'] }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>Rented</span>
                    <span class="badge bg-primary">{{ $stats['rented_units'] }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>Broken</span>
                    <span class="badge bg-danger">{{ $psUnits->where('status', 'broken')->count() }}</span>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4 mb-3">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h6 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    Quick Actions
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.rentals.create') }}" class="btn btn-primary btn-sm">
                        <i class="fas fa-plus me-1"></i>
                        New Rental
                    </a>
                    <a href="{{ route('admin.ps-units.create') }}" class="btn btn-success btn-sm">
                        <i class="fas fa-gamepad me-1"></i>
                        Add PS Unit
                    </a>
                    <a href="{{ route('admin.rentals.index') }}" class="btn btn-info btn-sm">
                        <i class="fas fa-list me-1"></i>
                        View Rentals
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Rentals -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0 text-white">
                    <i class="fas fa-history me-2"></i>
                    Recent Rentals
                </h5>
            </div>
            <div class="card-body">
                @if($recentRentals->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Rental Code</th>
                                    <th>Customer</th>
                                    <th>PS Unit</th>
                                    <th>Duration</th>
                                    <th>Total Price</th>
                                    <th>Status</th>
                                    <th>Date</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($recentRentals as $rental)
                                <tr>
                                    <td><strong class="text-primary">{{ $rental->rental_code }}</strong></td>
                                    <td>{{ $rental->customer->name }}</td>
                                    <td>
                                        <span class="badge bg-secondary">{{ $rental->psUnit->unit_code }}</span>
                                        {{ $rental->psUnit->ps_type }}
                                    </td>
                                    <td>{{ $rental->duration }} {{ $rental->rental_type === 'hourly' ? 'hours' : 'days' }}</td>
                                    <td>Rp {{ number_format($rental->total_price, 0, ',', '.') }}</td>
                                    <td>
                                        @php
                                            $statusClass = match($rental->status) {
                                                'pending' => 'warning',
                                                'active' => 'primary',
                                                'completed' => 'success',
                                                'cancelled' => 'secondary',
                                                'overdue' => 'danger',
                                                default => 'secondary'
                                            };
                                        @endphp
                                        <span class="badge bg-{{ $statusClass }}">{{ ucfirst($rental->status) }}</span>
                                    </td>
                                    <td>{{ $rental->created_at->format('d M Y') }}</td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">No recent rentals found.</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

@if($alertUnits->count() > 0)
<!-- Alert Units -->
<div class="row mt-4">
    <div class="col-12">
        <div class="card" style="border: none; box-shadow: 0 8px 25px rgba(96, 181, 255, 0.15);">
            <div class="card-header" style="background: #60B5FF; color: white; border: none; border-radius: 20px 20px 0 0;">
                <h4 class="mb-0 fw-bold">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Units Requiring Attention
                </h4>
            </div>
            <div class="card-body p-4">
                <div class="row">
                    @foreach($alertUnits as $unit)
                    <div class="col-md-4 mb-3">
                        <div class="alert mb-0 p-4" style="background: #F5F9FF; border: 3px solid #60B5FF; color: #2C3E50; border-radius: 15px;">
                            <div class="d-flex align-items-center">
                                <div class="me-3">
                                    <img src="{{ $unit->ps_image }}"
                                         alt="{{ $unit->ps_display_name }}"
                                         class="img-thumbnail"
                                         style="width: 50px; height: 50px; object-fit: contain;"
                                         onerror="this.src='{{ asset('images/ps-units/placeholder.svg') }}'">
                                </div>
                                <div>
                                    <strong class="fs-5">{{ $unit->unit_code }}</strong><br>
                                    <span class="text-muted">{{ $unit->ps_type }}</span><br>
                                    <span class="badge mt-1 px-2 py-1" style="background: #60B5FF; color: white; border-radius: 10px;">{{ ucfirst($unit->status) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </div>
</div>
@endif

@endsection
