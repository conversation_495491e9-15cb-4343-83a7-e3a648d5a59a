@extends('layouts.app')

@section('title', 'Kelola Customer - Rental PS Admin')
@section('page-title', 'Kelola Customer')

@section('content')
<div class="row mb-4">
    <div class="col-md-6">
        <h4>
            <i class="fas fa-users me-2"></i>
            Daftar Customer
        </h4>
    </div>
    <div class="col-md-6 text-end">
        <a href="{{ route('admin.customers.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>
            Customer Baru
        </a>
    </div>
</div>

<!-- Statistik Cards -->
<div class="row mb-4">
    <div class="col-md-2 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-primary">{{ $stats['total'] }}</h5>
                <small class="text-muted">Total</small>
            </div>
        </div>
    </div>

    <div class="col-md-2 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-info">{{ $stats['male'] }}</h5>
                <small class="text-muted">Laki-laki</small>
            </div>
        </div>
    </div>
    <div class="col-md-2 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-secondary">{{ $stats['female'] }}</h5>
                <small class="text-muted">Perempuan</small>
            </div>
        </div>
    </div>
</div>

<!-- Filter Form -->
<div class="card mb-4">
    <div class="card-header">
        <h6 class="mb-0">
            <i class="fas fa-filter me-2"></i>
            Filter & Pencarian
        </h6>
    </div>
    <div class="card-body">
        <form method="GET" action="{{ route('admin.customers.index') }}">
            <div class="row">

                <div class="col-md-3 mb-3">
                    <label class="form-label">Jenis Kelamin</label>
                    <select name="gender" class="form-select">
                        <option value="">Semua</option>
                        <option value="male" {{ request('gender') == 'male' ? 'selected' : '' }}>Laki-laki</option>
                        <option value="female" {{ request('gender') == 'female' ? 'selected' : '' }}>Perempuan</option>
                    </select>
                </div>
                <div class="col-md-6 mb-3">
                    <label class="form-label">Pencarian</label>
                    <input type="text" name="search" class="form-control" 
                           placeholder="Cari berdasarkan nama, email, atau telepon..." 
                           value="{{ request('search') }}">
                </div>
            </div>
            <div class="row">
                <div class="col-md-12 d-flex">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i>
                        Cari
                    </button>
                    <a href="{{ route('admin.customers.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        Reset
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Tabel Customer -->
<div class="card">
    <div class="card-header">
        <h6 class="mb-0">
            <i class="fas fa-list me-2"></i>
            Daftar Customer ({{ $customers->total() }} total)
        </h6>
    </div>
    <div class="card-body">
        @if($customers->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Nama</th>
                            <th>Kontak</th>
                            <th>Jenis Kelamin</th>
                            <th>Tanggal Lahir</th>
                            <th>Total Rental</th>

                            <th>Bergabung</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($customers as $customer)
                        <tr>
                            <td>
                                <div>
                                    <strong>{{ $customer->name }}</strong>
                                    @if($customer->id_card_number)
                                        <br><small class="text-muted">KTP: {{ $customer->id_card_number }}</small>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div>
                                    <i class="fas fa-envelope me-1"></i>{{ $customer->email }}<br>
                                    @if($customer->phone)
                                        <i class="fas fa-phone me-1"></i>{{ $customer->phone }}
                                    @endif
                                </div>
                            </td>
                            <td>
                                @if($customer->gender)
                                    @php
                                        $genderIcon = $customer->gender === 'male' ? 'mars' : 'venus';
                                        $genderText = $customer->gender === 'male' ? 'Laki-laki' : 'Perempuan';
                                        $genderClass = $customer->gender === 'male' ? 'text-primary' : 'text-danger';
                                    @endphp
                                    <i class="fas fa-{{ $genderIcon }} {{ $genderClass }} me-1"></i>
                                    {{ $genderText }}
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                            <td>
                                @if($customer->birth_date)
                                    {{ $customer->birth_date->format('d/m/Y') }}<br>
                                    <small class="text-muted">{{ $customer->birth_date->age }} tahun</small>
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                            <td>
                                <span class="badge bg-info">{{ $customer->rentals_count }}</span>
                            </td>

                            <td>
                                {{ $customer->created_at->format('d/m/Y') }}
                            </td>
                            <td class="text-center">
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.customers.show', $customer) }}"
                                       class="btn btn-outline-secondary"
                                       title="Lihat Detail">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.customers.edit', $customer) }}"
                                       class="btn btn-outline-warning"
                                       title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    @if($customer->rentals_count == 0)
                                        <form method="POST"
                                              action="{{ route('admin.customers.destroy', $customer) }}"
                                              style="display: contents;"
                                              onsubmit="return confirm('Yakin ingin menghapus customer ini?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit"
                                                    class="btn btn-outline-danger"
                                                    title="Hapus">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    @else
                                        <button class="btn btn-outline-danger"
                                                title="Cannot delete - Customer has rentals"
                                                disabled>
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $customers->appends(request()->query())->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Tidak ada customer ditemukan</h5>
                <p class="text-muted">Belum ada data customer atau coba ubah filter pencarian.</p>
                <a href="{{ route('admin.customers.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    Tambah Customer Baru
                </a>
            </div>
        @endif
    </div>
</div>
@endsection
