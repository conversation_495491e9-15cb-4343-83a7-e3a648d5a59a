@extends('layouts.app')

@section('title', 'Add PS Unit - Rental PS Admin')
@section('page-title', 'Add New PS Unit')

@section('content')
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-gamepad me-2"></i>
                    Add New PS Unit
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('admin.ps-units.store') }}" enctype="multipart/form-data">
                    @csrf
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="unit_code" class="form-label">Unit Code *</label>
                            <input type="text" 
                                   class="form-control @error('unit_code') is-invalid @enderror" 
                                   id="unit_code" 
                                   name="unit_code" 
                                   value="{{ old('unit_code') }}" 
                                   placeholder="e.g., PS001"
                                   required>
                            @error('unit_code')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="ps_type" class="form-label">PS Type *</label>
                            <select class="form-select @error('ps_type') is-invalid @enderror"
                                    id="ps_type"
                                    name="ps_type"
                                    onchange="updatePsPreview()"
                                    required>
                                <option value="">Select PS Type</option>
                                <option value="PlayStation 4" {{ old('ps_type') == 'PlayStation 4' ? 'selected' : '' }}>PlayStation 4</option>
                                <option value="PlayStation 4 Pro" {{ old('ps_type') == 'PlayStation 4 Pro' ? 'selected' : '' }}>PlayStation 4 Pro</option>
                                <option value="PlayStation 4 Slim" {{ old('ps_type') == 'PlayStation 4 Slim' ? 'selected' : '' }}>PlayStation 4 Slim</option>
                                <option value="PlayStation 5" {{ old('ps_type') == 'PlayStation 5' ? 'selected' : '' }}>PlayStation 5</option>
                                <option value="PlayStation 5 Pro" {{ old('ps_type') == 'PlayStation 5 Pro' ? 'selected' : '' }}>PlayStation 5 Pro</option>
                            </select>
                            @error('ps_type')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror

                            <!-- PS Image Preview -->
                            <div class="mt-3 text-center" id="ps-preview" style="display: none;">
                                <img id="ps-preview-image"
                                     src=""
                                     alt="PS Preview"
                                     class="img-thumbnail"
                                     style="max-width: 150px; max-height: 150px; object-fit: contain;"
                                     onerror="this.src='{{ asset('images/ps-units/placeholder.svg') }}'">
                                <p class="mt-2 text-muted small" id="ps-preview-name"></p>
                            </div>
                        </div>

                        <!-- Custom Image Upload -->
                        <div class="col-md-6 mb-3">
                            <label for="custom_image" class="form-label">Upload Custom Image (Optional)</label>
                            <input type="file"
                                   class="form-control @error('custom_image') is-invalid @enderror"
                                   id="custom_image"
                                   name="custom_image"
                                   accept="image/*"
                                   onchange="previewCustomImage(this)">
                            @error('custom_image')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">
                                Upload gambar khusus untuk unit ini. Jika tidak diupload, akan menggunakan gambar default berdasarkan tipe PS.
                            </div>

                            <!-- Custom Image Preview -->
                            <div class="mt-3 text-center" id="custom-preview" style="display: none;">
                                <img id="custom-preview-image"
                                     src=""
                                     alt="Custom Preview"
                                     class="img-thumbnail"
                                     style="max-width: 150px; max-height: 150px; object-fit: contain;">
                                <p class="mt-2 text-success small">Custom Image Preview</p>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="brand" class="form-label">Brand *</label>
                            <input type="text" 
                                   class="form-control @error('brand') is-invalid @enderror" 
                                   id="brand" 
                                   name="brand" 
                                   value="{{ old('brand', 'Sony') }}" 
                                   required>
                            @error('brand')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="condition" class="form-label">Condition *</label>
                            <select class="form-select @error('condition') is-invalid @enderror"
                                    id="condition"
                                    name="condition"
                                    required>
                                <option value="">Select Condition</option>
                                <option value="excellent" {{ old('condition') == 'excellent' ? 'selected' : '' }}>Excellent</option>
                                <option value="good" {{ old('condition', 'good') == 'good' ? 'selected' : '' }}>Good</option>
                                <option value="fair" {{ old('condition') == 'fair' ? 'selected' : '' }}>Fair</option>
                                <option value="poor" {{ old('condition') == 'poor' ? 'selected' : '' }}>Poor</option>
                            </select>
                            @error('condition')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="total_stock" class="form-label">Total Stock *</label>
                            <input type="number"
                                   class="form-control @error('total_stock') is-invalid @enderror"
                                   id="total_stock"
                                   name="total_stock"
                                   value="{{ old('total_stock', 1) }}"
                                   min="1"
                                   required>
                            @error('total_stock')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="available_stock" class="form-label">Available Stock *</label>
                            <input type="number"
                                   class="form-control @error('available_stock') is-invalid @enderror"
                                   id="available_stock"
                                   name="available_stock"
                                   value="{{ old('available_stock', 1) }}"
                                   min="0"
                                   required>
                            @error('available_stock')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="price_per_hour" class="form-label">Price per Hour (Rp) *</label>
                            <input type="number" 
                                   class="form-control @error('price_per_hour') is-invalid @enderror" 
                                   id="price_per_hour" 
                                   name="price_per_hour" 
                                   value="{{ old('price_per_hour') }}" 
                                   min="0"
                                   step="1000"
                                   placeholder="15000"
                                   required>
                            @error('price_per_hour')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="price_per_day" class="form-label">Price per Day (Rp) *</label>
                            <input type="number" 
                                   class="form-control @error('price_per_day') is-invalid @enderror" 
                                   id="price_per_day" 
                                   name="price_per_day" 
                                   value="{{ old('price_per_day') }}" 
                                   min="0"
                                   step="1000"
                                   placeholder="100000"
                                   required>
                            @error('price_per_day')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="status" class="form-label">Status *</label>
                            <select class="form-select @error('status') is-invalid @enderror"
                                    id="status"
                                    name="status"
                                    required>
                                <option value="">Select Status</option>
                                <option value="available" {{ old('status', 'available') == 'available' ? 'selected' : '' }}>Tersedia</option>
                                <option value="maintenance" {{ old('status') == 'maintenance' ? 'selected' : '' }}>Kosong</option>
                                <option value="broken" {{ old('status') == 'broken' ? 'selected' : '' }}>Rusak</option>
                            </select>
                            @error('status')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="purchase_date" class="form-label">Purchase Date</label>
                            <input type="date" 
                                   class="form-control @error('purchase_date') is-invalid @enderror" 
                                   id="purchase_date" 
                                   name="purchase_date" 
                                   value="{{ old('purchase_date') }}">
                            @error('purchase_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="accessories" class="form-label">Accessories</label>
                        <input type="text" 
                               class="form-control @error('accessories') is-invalid @enderror" 
                               id="accessories" 
                               name="accessories" 
                               value="{{ old('accessories') }}" 
                               placeholder="2 Controller, HDMI Cable, Power Cable, Game Disc (separate with comma)">
                        <div class="form-text">Separate multiple accessories with comma</div>
                        @error('accessories')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control @error('description') is-invalid @enderror" 
                                  id="description" 
                                  name="description" 
                                  rows="3"
                                  placeholder="Additional description about this PS unit">{{ old('description') }}</textarea>
                        @error('description')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <a href="{{ route('admin.ps-units.index') }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>
                            Back to List
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            Save PS Unit
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
@endsection

@section('scripts')
<script>
    // Auto calculate daily price based on hourly price
    document.getElementById('price_per_hour').addEventListener('input', function() {
        const hourlyPrice = parseFloat(this.value) || 0;
        const dailyPrice = hourlyPrice * 8; // 8 hours = 1 day
        document.getElementById('price_per_day').value = dailyPrice;
    });

    // Auto sync available stock with total stock
    document.getElementById('total_stock').addEventListener('input', function() {
        const totalStock = parseInt(this.value) || 0;
        const availableStockInput = document.getElementById('available_stock');
        const currentAvailable = parseInt(availableStockInput.value) || 0;

        // Set available stock to total stock if it's a new entry
        if (currentAvailable <= 1) {
            availableStockInput.value = totalStock;
        }

        // Set max attribute for available stock
        availableStockInput.setAttribute('max', totalStock);
    });

    // Validate available stock doesn't exceed total stock
    document.getElementById('available_stock').addEventListener('input', function() {
        const availableStock = parseInt(this.value) || 0;
        const totalStock = parseInt(document.getElementById('total_stock').value) || 0;

        if (availableStock > totalStock) {
            this.value = totalStock;
            alert('Available stock cannot exceed total stock!');
        }
    });

    // PS Image Preview Function
    function updatePsPreview() {
        const psType = document.getElementById('ps_type').value;
        const preview = document.getElementById('ps-preview');
        const previewImage = document.getElementById('ps-preview-image');
        const previewName = document.getElementById('ps-preview-name');

        if (psType) {
            // Map PS types to images
            const imageMap = {
                'PlayStation 4': '{{ asset("images/ps-units/ps4.jpg") }}',
                'PlayStation 4 Pro': '{{ asset("images/ps-units/ps4-pro.jpg") }}',
                'PlayStation 4 Slim': '{{ asset("images/ps-units/ps4-slim.jpg") }}',
                'PlayStation 5': '{{ asset("images/ps-units/ps5.jpg") }}',
                'PlayStation 5 Pro': '{{ asset("images/ps-units/ps5-pro.jpg") }}'
            };

            previewImage.src = imageMap[psType] || '{{ asset("images/ps-units/placeholder.svg") }}';
            previewName.textContent = psType;
            preview.style.display = 'block';
        } else {
            preview.style.display = 'none';
        }
    }

    // Initialize preview if there's an old value
    document.addEventListener('DOMContentLoaded', function() {
        updatePsPreview();
    });

    // Custom Image Preview Function
    function previewCustomImage(input) {
        const preview = document.getElementById('custom-preview');
        const previewImage = document.getElementById('custom-preview-image');

        if (input.files && input.files[0]) {
            const reader = new FileReader();

            reader.onload = function(e) {
                previewImage.src = e.target.result;
                preview.style.display = 'block';
            };

            reader.readAsDataURL(input.files[0]);
        } else {
            preview.style.display = 'none';
        }
    }
</script>
@endsection
