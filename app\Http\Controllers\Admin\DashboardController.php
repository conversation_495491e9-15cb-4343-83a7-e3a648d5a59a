<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\PsUnit;
use App\Models\Rental;
use App\Models\Customer;

class DashboardController extends Controller
{
    // Middleware handled in routes

    public function index()
    {
        // Statistik untuk dashboard
        $stats = [
            'total_ps_units' => PsUnit::count(),
            'available_units' => PsUnit::where('available_stock', '>', 0)->count(),
            'rented_units' => PsUnit::where('available_stock', 0)->count(),
            'maintenance_units' => PsUnit::where('status', 'maintenance')->count(),
            'total_customers' => Customer::count(),
            'active_rentals' => Rental::where('status', 'active')->count(),
            'pending_rentals' => Rental::where('status', 'pending')->count(),
            'overdue_rentals' => Rental::where('status', 'overdue')->count(),
            'today_revenue' => Rental::whereDate('created_at', today())
                                   ->where('payment_status', 'paid')
                                   ->sum('final_amount'),
            'month_revenue' => Rental::whereMonth('created_at', now()->month)
                                   ->whereYear('created_at', now()->year)
                                   ->where('payment_status', 'paid')
                                   ->sum('final_amount'),
            // Stock statistics
            'total_stock' => PsUnit::sum('total_stock'),
            'available_stock' => PsUnit::sum('available_stock'),
            'low_stock_units' => PsUnit::whereRaw('available_stock < total_stock * 0.3')->count(),
        ];

        // Recent rentals
        $recentRentals = Rental::with(['customer', 'psUnit'])
                              ->orderBy('created_at', 'desc')
                              ->limit(10)
                              ->get();

        // PS Units yang perlu perhatian
        $alertUnits = PsUnit::where('status', 'broken')->get();

        // All PS Units untuk statistik
        $psUnits = PsUnit::all();

        return view('admin.dashboard', compact('stats', 'recentRentals', 'alertUnits', 'psUnits'));
    }
}
