@extends('layouts.app')

@section('title', '<PERSON><PERSON><PERSON> Rental - Rental PS Admin')
@section('page-title', 'Kelola Rental')

@section('content')
<div class="row mb-4">
    <div class="col-md-6">
        <h4>
            <i class="fas fa-calendar-alt me-2"></i>
            Daftar Rental
        </h4>
    </div>
    <div class="col-md-6 text-end">
        <a href="{{ route('admin.rentals.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>
            Rental Baru
        </a>
    </div>
</div>

<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-2 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-primary">{{ $stats['total'] }}</h5>
                <small class="text-muted">Total</small>
            </div>
        </div>
    </div>
    <div class="col-md-2 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-warning">{{ $stats['pending'] }}</h5>
                <small class="text-muted">Pending</small>
            </div>
        </div>
    </div>
    <div class="col-md-2 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-success">{{ $stats['active'] }}</h5>
                <small class="text-muted">Aktif</small>
            </div>
        </div>
    </div>
    <div class="col-md-2 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-info">{{ $stats['completed'] }}</h5>
                <small class="text-muted">Selesai</small>
            </div>
        </div>
    </div>
    <div class="col-md-2 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-danger">{{ $stats['overdue'] }}</h5>
                <small class="text-muted">Terlambat</small>
            </div>
        </div>
    </div>
    <div class="col-md-2 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="text-secondary">{{ $stats['cancelled'] }}</h5>
                <small class="text-muted">Dibatalkan</small>
            </div>
        </div>
    </div>
</div>

<!-- Filter Form -->
<div class="card mb-4">
    <div class="card-header">
        <h6 class="mb-0">
            <i class="fas fa-filter me-2"></i>
            Filter & Pencarian
        </h6>
    </div>
    <div class="card-body">
        <form method="GET" action="{{ route('admin.rentals.index') }}">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <label class="form-label">Status Rental</label>
                    <select name="status" class="form-select">
                        <option value="">Semua Status</option>
                        <option value="pending" {{ request()->get('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                        <option value="active" {{ request()->get('status') == 'active' ? 'selected' : '' }}>Aktif</option>
                        <option value="completed" {{ request()->get('status') == 'completed' ? 'selected' : '' }}>Selesai</option>
                        <option value="overdue" {{ request()->get('status') == 'overdue' ? 'selected' : '' }}>Terlambat</option>
                        <option value="cancelled" {{ request()->get('status') == 'cancelled' ? 'selected' : '' }}>Dibatalkan</option>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label class="form-label">Status Pembayaran</label>
                    <select name="payment_status" class="form-select">
                        <option value="">Semua Status</option>
                        <option value="unpaid" {{ request()->get('payment_status') == 'unpaid' ? 'selected' : '' }}>Belum Bayar</option>
                        <option value="partial" {{ request()->get('payment_status') == 'partial' ? 'selected' : '' }}>Sebagian</option>
                        <option value="paid" {{ request()->get('payment_status') == 'paid' ? 'selected' : '' }}>Lunas</option>
                        <option value="refunded" {{ request()->get('payment_status') == 'refunded' ? 'selected' : '' }}>Dikembalikan</option>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label class="form-label">Dari Tanggal</label>
                    <input type="date" name="date_from" class="form-control" value="{{ request()->get('date_from') }}">
                </div>
                <div class="col-md-3 mb-3">
                    <label class="form-label">Sampai Tanggal</label>
                    <input type="date" name="date_to" class="form-control" value="{{ request()->get('date_to') }}">
                </div>
            </div>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label class="form-label">Pencarian</label>
                    <input type="text" name="search" class="form-control"
                           placeholder="Cari berdasarkan kode rental atau nama customer..."
                           value="{{ request()->get('search') }}">
                </div>
                <div class="col-md-6 mb-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i>
                        Cari
                    </button>
                    <a href="{{ route('admin.rentals.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        Reset
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Tabel Rental -->
<div class="card">
    <div class="card-header">
        <h6 class="mb-0">
            <i class="fas fa-list me-2"></i>
            Daftar Rental ({{ $rentals->total() }} total)
        </h6>
    </div>
    <div class="card-body">
        @if($rentals->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Kode Rental</th>
                            <th>Customer</th>
                            <th>PS Unit</th>
                            <th>Waktu Rental</th>
                            <th>Durasi</th>
                            <th>Total Harga</th>
                            <th>Status</th>
                            <th>Pembayaran</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($rentals as $rental)
                        <tr class="border-bottom">
                            <td class="px-4 py-3">
                                <div class="d-flex align-items-center">
                                    <div class="rounded-circle d-flex align-items-center justify-content-center me-3"
                                         style="width: 40px; height: 40px; background: #E3F2FD;">
                                        <i class="fas fa-receipt text-primary"></i>
                                    </div>
                                    <div>
                                        <div class="fw-bold text-primary">{{ $rental->rental_code }}</div>
                                        <small class="text-muted">{{ $rental->created_at->format('d M Y') }}</small>
                                    </div>
                                </div>
                            </td>
                            <td class="py-3">
                                <div class="d-flex align-items-center">
                                    <div class="rounded-circle d-flex align-items-center justify-content-center me-3"
                                         style="width: 35px; height: 35px; background: #60B5FF; color: white;">
                                        <i class="fas fa-user"></i>
                                    </div>
                                    <div>
                                        <div class="fw-semibold text-dark">{{ $rental->customer->name }}</div>
                                        <small class="text-muted">{{ $rental->customer->phone }}</small>
                                    </div>
                                </div>
                            </td>
                            <td class="py-3">
                                <div class="d-flex align-items-center">
                                    <div class="rounded-circle d-flex align-items-center justify-content-center me-3"
                                         style="width: 35px; height: 35px; background: #1976D2; color: white;">
                                        <i class="fas fa-gamepad"></i>
                                    </div>
                                    <div>
                                        <span class="badge bg-primary px-2 py-1">{{ $rental->psUnit->unit_code }}</span>
                                        <div class="small text-muted mt-1">{{ $rental->psUnit->ps_type }}</div>
                                    </div>
                                </div>
                            </td>
                            <td class="py-3">
                                <div class="small">
                                    <div class="text-dark fw-semibold">
                                        <i class="fas fa-play text-success me-1"></i>
                                        {{ $rental->start_datetime->format('d/m H:i') }}
                                    </div>
                                    <div class="text-muted">
                                        <i class="fas fa-stop text-danger me-1"></i>
                                        {{ $rental->end_datetime->format('d/m H:i') }}
                                    </div>
                                </div>
                            </td>
                            <td class="py-3">
                                <span class="badge bg-light text-dark px-3 py-2 fw-semibold">
                                    {{ $rental->duration }} {{ $rental->rental_type === 'hourly' ? 'Hours' : 'Days' }}
                                </span>
                            </td>
                            <td class="py-3">
                                <div class="fw-bold text-dark">Rp {{ number_format($rental->final_amount, 0, ',', '.') }}</div>
                                @if($rental->penalty > 0)
                                    <div class="small text-danger mt-1">
                                        <i class="fas fa-exclamation-triangle me-1"></i>
                                        Penalty: Rp {{ number_format($rental->penalty, 0, ',', '.') }}
                                    </div>
                                @endif
                            </td>
                            <td class="py-3">
                                @php
                                    $statusClass = match($rental->status) {
                                        'pending' => 'warning',
                                        'active' => 'primary',
                                        'completed' => 'success',
                                        'cancelled' => 'secondary',
                                        'overdue' => 'danger',
                                        default => 'secondary'
                                    };
                                @endphp
                                <span class="badge bg-{{ $statusClass }}">{{ ucfirst($rental->status) }}</span>
                            </td>
                            <td class="py-3">
                                @php
                                    $paymentClass = match($rental->payment_status) {
                                        'unpaid' => 'danger',
                                        'partial' => 'warning',
                                        'paid' => 'success',
                                        'refunded' => 'info',
                                        default => 'secondary'
                                    };
                                @endphp
                                <span class="badge bg-{{ $paymentClass }}">{{ ucfirst($rental->payment_status) }}</span>
                            </td>
                            <td class="text-center">
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.rentals.show', $rental) }}"
                                       class="btn btn-outline-secondary"
                                       title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.rentals.edit', $rental) }}"
                                       class="btn btn-outline-warning"
                                       title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    @if($rental->status !== 'active')
                                        <form method="POST"
                                              action="{{ route('admin.rentals.destroy', $rental) }}"
                                              style="display: contents;"
                                              onsubmit="return confirm('Are you sure you want to delete this rental?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit"
                                                    class="btn btn-outline-danger"
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    @else
                                        <button class="btn btn-outline-danger"
                                                title="Cannot delete - Rental is active"
                                                disabled>
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $rentals->appends(request()->query())->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Tidak ada rental ditemukan</h5>
                <p class="text-muted">Belum ada data rental atau coba ubah filter pencarian.</p>
                <a href="{{ route('admin.rentals.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    Buat Rental Baru
                </a>
            </div>
        @endif
    </div>
</div>
@endsection
