<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\User;
use Illuminate\Support\Facades\Hash;

class AdminUserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        User::create([
            'name' => 'Admin Rental PS',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('admin123'),
            'role' => 'admin',
            'status' => 'active',
        ]);

        // Create staff user
        User::create([
            'name' => 'Staff Rental PS',
            'email' => '<EMAIL>',
            'email_verified_at' => now(),
            'password' => Hash::make('staff123'),
            'role' => 'staff',
            'status' => 'active',
        ]);
    }
}
