<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Rental;
use App\Models\PsUnit;
use App\Models\Customer;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class RentalController extends Controller
{
    /**
     * Tampilkan daftar semua rental
     */
    public function index(Request $request)
    {
        try {
            $query = Rental::with(['customer', 'psUnit']);

            // Filter berdasarkan status
            if ($request->filled('status')) {
                $query->where('status', $request->get('status'));
            }

            // Filter berdasarkan payment status
            if ($request->filled('payment_status')) {
                $query->where('payment_status', $request->get('payment_status'));
            }

            // Filter berdasarkan tanggal
            if ($request->filled('date_from')) {
                try {
                    $query->whereDate('created_at', '>=', $request->get('date_from'));
                } catch (\Exception $e) {
                    // Invalid date format, ignore filter
                }
            }
            if ($request->filled('date_to')) {
                try {
                    $query->whereDate('created_at', '<=', $request->get('date_to'));
                } catch (\Exception $e) {
                    // Invalid date format, ignore filter
                }
            }

            // Search berdasarkan rental code atau customer name
            if ($request->filled('search')) {
                $search = $request->get('search');
                $query->where(function($q) use ($search) {
                    $q->where('rental_code', 'like', "%{$search}%")
                      ->orWhereHas('customer', function($customerQuery) use ($search) {
                          $customerQuery->where('name', 'like', "%{$search}%");
                      });
                });
            }

            $rentals = $query->orderBy('created_at', 'desc')->paginate(15);

            // Statistik untuk filter
            $stats = [
                'total' => Rental::count(),
                'pending' => Rental::where('status', 'pending')->count(),
                'active' => Rental::where('status', 'active')->count(),
                'completed' => Rental::where('status', 'completed')->count(),
                'overdue' => Rental::where('status', 'overdue')->count(),
                'cancelled' => Rental::where('status', 'cancelled')->count(),
            ];

            return view('admin.rentals.index', compact('rentals', 'stats'));

        } catch (\Exception $e) {
            Log::error('Error loading rentals index: ' . $e->getMessage());

            // Fallback data dengan pagination yang benar
            $rentals = new \Illuminate\Pagination\LengthAwarePaginator(
                collect([]), // empty collection
                0, // total items
                15, // per page
                1, // current page
                ['path' => request()->url(), 'pageName' => 'page']
            );

            $stats = [
                'total' => 0,
                'pending' => 0,
                'active' => 0,
                'completed' => 0,
                'overdue' => 0,
                'cancelled' => 0,
            ];

            return view('admin.rentals.index', compact('rentals', 'stats'))
                    ->with('error', 'Terjadi kesalahan saat memuat data rental.');
        }
    }

    /**
     * Tampilkan form untuk membuat rental baru
     */
    public function create(Request $request)
    {
        // Ambil PS Units yang masih memiliki stok tersedia
        $psUnits = PsUnit::where('available_stock', '>', 0)
                         ->where('status', '!=', 'broken')
                         ->get();
        $customers = Customer::all();

        // Debug: cek apakah data ada
        if ($psUnits->isEmpty()) {
            session()->flash('warning', 'Tidak ada PS Unit yang tersedia saat ini.');
        }

        if ($customers->isEmpty()) {
            session()->flash('warning', 'Tidak ada Customer saat ini.');
        }

        // Pre-select customer jika ada parameter
        $selectedCustomerId = $request->get('customer_id');

        return view('admin.rentals.create', compact('psUnits', 'customers', 'selectedCustomerId'));
    }

    /**
     * Simpan rental baru ke database
     */
    public function store(Request $request)
    {
        try {
            $request->validate([
                'customer_id' => 'required|exists:customers,id',
                'ps_unit_id' => 'required|exists:ps_units,id',
                'rental_type' => 'required|in:hourly,daily',
                'duration' => 'required|integer|min:1',
                'start_datetime' => 'required|date|after_or_equal:now',
                'notes' => 'nullable|string|max:500',
            ], [
                'customer_id.required' => 'Customer harus dipilih.',
                'customer_id.exists' => 'Customer yang dipilih tidak valid.',
                'ps_unit_id.required' => 'PS Unit harus dipilih.',
                'ps_unit_id.exists' => 'PS Unit yang dipilih tidak valid.',
                'rental_type.required' => 'Tipe rental harus dipilih.',
                'rental_type.in' => 'Tipe rental tidak valid.',
                'duration.required' => 'Durasi harus diisi.',
                'duration.integer' => 'Durasi harus berupa angka.',
                'duration.min' => 'Durasi minimal 1.',
                'start_datetime.required' => 'Waktu mulai harus diisi.',
                'start_datetime.date' => 'Format waktu mulai tidak valid.',
                'start_datetime.after_or_equal' => 'Waktu mulai tidak boleh di masa lalu.',
                'notes.max' => 'Catatan maksimal 500 karakter.',
            ]);

            $psUnit = PsUnit::find($request->get('ps_unit_id'));

            if (!$psUnit) {
                return back()->withErrors(['ps_unit_id' => 'PS Unit tidak ditemukan.']);
            }

            // Cek apakah unit masih memiliki stok tersedia
            if ($psUnit->getAttribute('available_stock') <= 0) {
                return back()->withErrors(['ps_unit_id' => 'PS Unit tidak memiliki stok tersedia.']);
            }

            // Cek apakah unit dalam kondisi baik (tidak rusak)
            if ($psUnit->getAttribute('status') === 'broken') {
                return back()->withErrors(['ps_unit_id' => 'PS Unit sedang dalam perbaikan.']);
            }

            $customer = Customer::find($request->get('customer_id'));
            if (!$customer) {
                return back()->withErrors(['customer_id' => 'Customer tidak ditemukan.']);
            }

            // Additional validation for hourly rentals
            if ($request->get('rental_type') === 'hourly') {
                $startDatetime = Carbon::parse($request->get('start_datetime'));
                $endOfDay = $startDatetime->copy()->endOfDay(); // 23:59:59
                $duration = (int) $request->get('duration');

                // Hitung jam maksimal yang tersisa di hari yang sama
                $maxHours = $endOfDay->diffInHours($startDatetime, false);

                // Jika start time sudah lewat dari hari ini, set maksimal 24 jam
                if ($maxHours <= 0) {
                    $maxHours = 24;
                }

                // Minimal 1 jam, maksimal 24 jam per hari
                $maxHours = max(1, min(24, $maxHours));

                if ($duration > $maxHours) {
                    return back()->withErrors([
                        'duration' => "Durasi maksimal untuk rental per jam adalah {$maxHours} jam pada hari yang dipilih."
                    ]);
                }
            }

            // Convert duration to integer
            $duration = (int) $request->get('duration');

            // Hitung harga
            $pricePerUnit = $request->get('rental_type') === 'hourly'
                           ? $psUnit->getAttribute('price_per_hour')
                           : $psUnit->getAttribute('price_per_day');

            $totalPrice = $pricePerUnit * $duration;
            $deposit = $totalPrice * 0.2; // 20% deposit

            // Hitung waktu selesai
            $startDatetime = Carbon::parse($request->get('start_datetime'));
            $endDatetime = $request->get('rental_type') === 'hourly'
                          ? $startDatetime->copy()->addHours($duration)
                          : $startDatetime->copy()->addDays($duration);

            // Cek apakah PS Unit tersedia untuk periode yang diminta
            if (!$this->isPsUnitAvailable($request->get('ps_unit_id'), $startDatetime, $endDatetime)) {
                return back()->withErrors(['ps_unit_id' => 'PS Unit tidak tersedia untuk periode waktu yang dipilih.']);
            }

            // Use database transaction untuk memastikan data consistency
            $rental = DB::transaction(function () use ($request, $psUnit, $startDatetime, $endDatetime, $pricePerUnit, $totalPrice, $deposit, $duration) {
                // Generate rental code (lebih aman untuk concurrent access)
                $rentalCode = $this->generateRentalCode();

                $rental = Rental::create([
                    'rental_code' => $rentalCode,
                    'customer_id' => $request->get('customer_id'),
                    'ps_unit_id' => $request->get('ps_unit_id'),
                    'start_datetime' => $startDatetime,
                    'end_datetime' => $endDatetime,
                    'rental_type' => $request->get('rental_type'),
                    'duration' => $duration, // Use converted integer
                    'price_per_unit' => $pricePerUnit,
                    'total_price' => $totalPrice,
                    'deposit' => $deposit,
                    'final_amount' => $totalPrice,
                    'status' => 'pending',
                    'payment_status' => 'unpaid',
                    'notes' => $request->get('notes'),
                ]);

                // Kurangi stok PS Unit
                $psUnit->decreaseStock(1);

                return $rental;
            });

            return redirect()->route('admin.rentals.show', $rental)
                            ->with('success', 'Rental berhasil dibuat!');

        } catch (\Exception $e) {
            Log::error('Error creating rental: ' . $e->getMessage());
            return back()->withErrors(['error' => 'Terjadi kesalahan saat membuat rental. Silakan coba lagi.'])
                        ->withInput();
        }
    }

    /**
     * Tampilkan detail rental
     */
    public function show(Rental $rental)
    {
        try {
            $rental->load(['customer', 'psUnit']);

            // Cek apakah relationships ada
            if (!$rental->customer || !$rental->psUnit) {
                return redirect()->route('admin.rentals.index')
                                ->withErrors(['error' => 'Data rental tidak lengkap atau rusak.']);
            }

            return view('admin.rentals.show', compact('rental'));

        } catch (\Exception $e) {
            Log::error('Error loading rental details: ' . $e->getMessage());
            return redirect()->route('admin.rentals.index')
                            ->withErrors(['error' => 'Terjadi kesalahan saat memuat detail rental.']);
        }
    }

    /**
     * Tampilkan form edit rental
     */
    public function edit(Rental $rental)
    {
        try {
            // Load relationships
            $rental->load(['customer', 'psUnit']);

            $psUnits = PsUnit::where('status', 'available')
                             ->orWhere('id', $rental->getAttribute('ps_unit_id'))
                             ->get();
            $customers = Customer::all();

            return view('admin.rentals.edit', compact('rental', 'psUnits', 'customers'));

        } catch (\Exception $e) {
            Log::error('Error loading rental edit form: ' . $e->getMessage());
            return redirect()->route('admin.rentals.index')
                            ->withErrors(['error' => 'Terjadi kesalahan saat memuat form edit.']);
        }
    }

    /**
     * Update rental di database
     */
    public function update(Request $request, Rental $rental)
    {
        try {
            $request->validate([
                'status' => 'required|in:pending,active,completed,cancelled,overdue',
                'payment_status' => 'required|in:unpaid,partial,paid,refunded',
                'notes' => 'nullable|string|max:500',
            ], [
                'status.required' => 'Status rental harus dipilih.',
                'status.in' => 'Status rental tidak valid.',
                'payment_status.required' => 'Status pembayaran harus dipilih.',
                'payment_status.in' => 'Status pembayaran tidak valid.',
                'notes.max' => 'Catatan maksimal 500 karakter.',
            ]);

            $oldStatus = $rental->getAttribute('status');

            // Validasi perubahan status
            if ($oldStatus === 'completed' && $request->get('status') !== 'completed') {
                return back()->withErrors(['status' => 'Rental yang sudah selesai tidak dapat diubah statusnya.']);
            }

            // Use database transaction
            DB::transaction(function () use ($rental, $request, $oldStatus) {
                $rental->update([
                    'status' => $request->get('status'),
                    'payment_status' => $request->get('payment_status'),
                    'notes' => $request->get('notes'),
                ]);

                // Update stok PS Unit berdasarkan status rental
                if ($request->get('status') === 'completed' || $request->get('status') === 'cancelled') {
                    // Kembalikan stok
                    $rental->psUnit->increaseStock(1);

                    if ($request->get('status') === 'completed' && !$rental->getAttribute('actual_return_datetime')) {
                        $rental->update(['actual_return_datetime' => now()]);
                    }
                } elseif ($request->get('status') === 'active' && $oldStatus === 'pending') {
                    // Kurangi stok jika dari pending ke active
                    $rental->psUnit->decreaseStock(1);
                }
            });

            return redirect()->route('admin.rentals.show', $rental)
                            ->with('success', 'Rental berhasil diupdate!');

        } catch (\Exception $e) {
            Log::error('Error updating rental: ' . $e->getMessage());
            return back()->withErrors(['error' => 'Terjadi kesalahan saat mengupdate rental. Silakan coba lagi.'])
                        ->withInput();
        }
    }

    /**
     * Hapus rental dari database
     */
    public function destroy(Rental $rental)
    {
        try {
            if ($rental->getAttribute('status') === 'active') {
                return back()->withErrors(['error' => 'Tidak dapat menghapus rental yang sedang aktif.']);
            }

            // Kembalikan stok PS Unit jika rental dibatalkan
            if ($rental->getAttribute('status') !== 'completed' && $rental->psUnit) {
                $rental->psUnit->increaseStock(1);
            }

            $rental->delete();

            return redirect()->route('admin.rentals.index')
                            ->with('success', 'Rental berhasil dihapus!');

        } catch (\Exception $e) {
            Log::error('Error deleting rental: ' . $e->getMessage());
            return back()->withErrors(['error' => 'Terjadi kesalahan saat menghapus rental. Silakan coba lagi.']);
        }
    }

    /**
     * Proses pengembalian PS Unit
     */
    public function returnUnit(Request $request, Rental $rental)
    {
        try {
            if ($rental->getAttribute('status') !== 'active') {
                return back()->withErrors(['error' => 'Hanya rental aktif yang bisa dikembalikan.']);
            }

            if ($rental->getAttribute('actual_return_datetime')) {
                return back()->withErrors(['error' => 'PS Unit sudah dikembalikan sebelumnya.']);
            }

            $returnDatetime = now();
            $penalty = 0;

            // Hitung denda jika terlambat
            if ($returnDatetime > $rental->getAttribute('end_datetime')) {
                $overdueDuration = $returnDatetime->diffInHours($rental->getAttribute('end_datetime'));
                $penaltyRate = $rental->getAttribute('price_per_unit') * 0.1; // 10% dari harga per unit
                $penalty = max(0, $overdueDuration * $penaltyRate); // Pastikan penalty tidak negatif
            }

            // Simpan nilai yang diperlukan sebelum transaction
            $currentTotalPrice = $rental->getAttribute('total_price');

            // Update rental dengan database transaction
            DB::transaction(function () use ($rental, $returnDatetime, $penalty, $currentTotalPrice) {
                $rental->update([
                    'actual_return_datetime' => $returnDatetime,
                    'penalty' => $penalty,
                    'final_amount' => $currentTotalPrice + $penalty,
                    'status' => 'completed',
                ]);

                // Kembalikan stok PS Unit
                $rental->psUnit->increaseStock(1);
            });

            $message = $penalty > 0
                      ? "PS Unit berhasil dikembalikan dengan denda Rp " . number_format($penalty, 0, ',', '.')
                      : "PS Unit berhasil dikembalikan tanpa denda.";

            return redirect()->route('admin.rentals.show', $rental)
                            ->with('success', $message);

        } catch (\Exception $e) {
            Log::error('Error returning unit: ' . $e->getMessage());
            return back()->withErrors(['error' => 'Terjadi kesalahan saat mengembalikan unit. Silakan coba lagi.']);
        }
    }

    /**
     * Perpanjang waktu rental
     */
    public function extend(Request $request, Rental $rental)
    {
        try {
            $request->validate([
                'extend_duration' => 'required|integer|min:1|max:30',
            ]);

            if ($rental->getAttribute('status') !== 'active') {
                return back()->withErrors(['error' => 'Hanya rental aktif yang bisa diperpanjang.']);
            }

            if ($rental->getAttribute('actual_return_datetime')) {
                return back()->withErrors(['error' => 'Rental sudah selesai, tidak bisa diperpanjang.']);
            }

            $extendDuration = (int) $request->get('extend_duration'); // Convert to integer
            $additionalPrice = $rental->getAttribute('price_per_unit') * $extendDuration;

            // Simpan nilai yang diperlukan sebelum transaction
            $currentDuration = $rental->getAttribute('duration');
            $currentTotalPrice = $rental->getAttribute('total_price');
            $currentFinalAmount = $rental->getAttribute('final_amount');
            $rentalType = $rental->getAttribute('rental_type');

            // Perpanjang waktu selesai
            $newEndDatetime = $rentalType === 'hourly'
                            ? $rental->getAttribute('end_datetime')->copy()->addHours($extendDuration)
                            : $rental->getAttribute('end_datetime')->copy()->addDays($extendDuration);

            // Update rental dengan database transaction
            DB::transaction(function () use ($rental, $newEndDatetime, $extendDuration, $additionalPrice, $currentDuration, $currentTotalPrice, $currentFinalAmount) {
                $rental->update([
                    'end_datetime' => $newEndDatetime,
                    'duration' => $currentDuration + $extendDuration,
                    'total_price' => $currentTotalPrice + $additionalPrice,
                    'final_amount' => $currentFinalAmount + $additionalPrice,
                ]);
            });

            // Refresh rental untuk mendapatkan data terbaru
            $rental->refresh();

            $rentalTypeText = $rentalType === 'hourly' ? 'jam' : 'hari';
            $successMessage = "Rental berhasil diperpanjang {$extendDuration} {$rentalTypeText} dengan tambahan biaya Rp " . number_format($additionalPrice, 0, ',', '.');

            return redirect()->route('admin.rentals.show', $rental)
                            ->with('success', $successMessage);

        } catch (\Exception $e) {
            Log::error('Error extending rental: ' . $e->getMessage());
            return back()->withErrors(['error' => 'Terjadi kesalahan saat memperpanjang rental. Silakan coba lagi.']);
        }
    }

    /**
     * Generate unique rental code
     */
    private function generateRentalCode()
    {
        // Generate rental code berdasarkan timestamp dan random untuk menghindari collision
        $timestamp = now()->format('ymdHis');
        $random = str_pad(mt_rand(1, 999), 3, '0', STR_PAD_LEFT);
        $rentalCode = 'RNT' . $timestamp . $random;

        // Jika masih ada duplicate (sangat jarang), tambahkan counter
        $counter = 1;
        $originalCode = $rentalCode;
        while (Rental::where('rental_code', $rentalCode)->exists()) {
            $rentalCode = $originalCode . str_pad($counter, 2, '0', STR_PAD_LEFT);
            $counter++;
        }

        return $rentalCode;
    }

    /**
     * Check if PS Unit is available for rental
     */
    private function isPsUnitAvailable($psUnitId, $startDatetime, $endDatetime, $excludeRentalId = null)
    {
        try {
            // Dengan sistem stok, cek apakah masih ada stok tersedia
            $psUnit = PsUnit::find($psUnitId);

            if (!$psUnit) {
                return false;
            }

            // Jika masih ada stok tersedia, unit bisa disewa
            if ($psUnit->getAttribute('available_stock') > 0) {
                return true;
            }

            // Jika stok habis, cek apakah ada rental yang akan selesai sebelum waktu yang diminta
            $start = $startDatetime instanceof Carbon ? $startDatetime : Carbon::parse($startDatetime);

            $activeRentals = Rental::where('ps_unit_id', $psUnitId)
                                  ->whereIn('status', ['pending', 'active'])
                                  ->where('end_datetime', '<=', $start)
                                  ->count();

            // Jika ada rental yang selesai sebelum waktu diminta, unit tersedia
            return $activeRentals > 0;

        } catch (\Exception $e) {
            Log::error('Error checking PS unit availability: ' . $e->getMessage());
            // Default to not available if there's an error
            return false;
        }
    }
}
