<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ps_units', function (Blueprint $table) {
            $table->id();
            $table->string('unit_code')->unique(); // Kode unit PS (PS001, PS002, dll)
            $table->string('ps_type'); // PS4, PS5, dll
            $table->string('brand')->default('Sony'); // Merk PlayStation
            $table->text('description')->nullable(); // Deskripsi unit
            $table->decimal('price_per_hour', 10, 2); // Harga per jam
            $table->decimal('price_per_day', 10, 2); // Harga per hari
            $table->enum('status', ['available', 'rented', 'maintenance', 'broken'])->default('available');
            $table->json('accessories')->nullable(); // Aks<PERSON>oris yang disertakan (controller, kabel, dll)
            $table->string('condition')->default('good'); // Kondisi unit
            $table->date('purchase_date')->nullable(); // Tanggal pembelian
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ps_units');
    }
};
