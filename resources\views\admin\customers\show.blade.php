@extends('layouts.app')

@section('title', 'Detail Customer - Rental PS Admin')
@section('page-title', 'Detail Customer')

@section('content')
<div class="row">
    <div class="col-md-8">
        <!-- Customer Information -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-user me-2"></i>
                    Informasi Customer
                </h5>

            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold">Nama:</td>
                                <td>{{ $customer->name }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Email:</td>
                                <td>{{ $customer->email }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Telepon:</td>
                                <td>{{ $customer->phone ?? '-' }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold"><PERSON><PERSON>in:</td>
                                <td>
                                    @if($customer->gender)
                                        @php
                                            $genderIcon = $customer->gender === 'male' ? 'mars' : 'venus';
                                            $genderText = $customer->gender === 'male' ? 'Laki-laki' : 'Perempuan';
                                            $genderClass = $customer->gender === 'male' ? 'text-primary' : 'text-danger';
                                        @endphp
                                        <i class="fas fa-{{ $genderIcon }} {{ $genderClass }} me-1"></i>
                                        {{ $genderText }}
                                    @else
                                        -
                                    @endif
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Tanggal Lahir:</td>
                                <td>
                                    @if($customer->birth_date)
                                        {{ $customer->birth_date->format('d F Y') }}
                                        <br><small class="text-muted">{{ $customer->birth_date->age }} tahun</small>
                                    @else
                                        -
                                    @endif
                                </td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold">Nomor KTP:</td>
                                <td>{{ $customer->id_card_number ?? '-' }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Alamat:</td>
                                <td>{{ $customer->address ?? '-' }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Bergabung:</td>
                                <td>{{ $customer->created_at->format('d F Y') }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Terakhir Update:</td>
                                <td>{{ $customer->updated_at->format('d F Y H:i') }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rental Statistics -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    Statistik Rental
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 text-center">
                        <div class="border rounded p-3">
                            <h4 class="text-primary">{{ $rentalStats['total_rentals'] }}</h4>
                            <small class="text-muted">Total Rental</small>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="border rounded p-3">
                            <h4 class="text-warning">{{ $rentalStats['active_rentals'] }}</h4>
                            <small class="text-muted">Rental Aktif</small>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="border rounded p-3">
                            <h4 class="text-success">{{ $rentalStats['completed_rentals'] }}</h4>
                            <small class="text-muted">Selesai</small>
                        </div>
                    </div>
                    <div class="col-md-3 text-center">
                        <div class="border rounded p-3">
                            <h4 class="text-info">Rp {{ number_format($rentalStats['total_spent'], 0, ',', '.') }}</h4>
                            <small class="text-muted">Total Pengeluaran</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Rental History -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-history me-2"></i>
                    Riwayat Rental ({{ $customer->rentals->count() }})
                </h5>
            </div>
            <div class="card-body">
                @if($customer->rentals->count() > 0)
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Kode Rental</th>
                                    <th>PS Unit</th>
                                    <th>Tanggal</th>
                                    <th>Durasi</th>
                                    <th>Total</th>
                                    <th>Status</th>
                                    <th>Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach($customer->rentals->take(10) as $rental)
                                <tr>
                                    <td>
                                        <strong class="text-primary">{{ $rental->rental_code }}</strong>
                                    </td>
                                    <td>
                                        <span class="badge bg-secondary">{{ $rental->psUnit->unit_code }}</span><br>
                                        <small>{{ $rental->psUnit->ps_type }}</small>
                                    </td>
                                    <td>
                                        {{ $rental->start_datetime->format('d/m/Y') }}<br>
                                        <small class="text-muted">{{ $rental->start_datetime->format('H:i') }}</small>
                                    </td>
                                    <td>
                                        {{ $rental->duration }} {{ $rental->rental_type === 'hourly' ? 'jam' : 'hari' }}
                                    </td>
                                    <td>
                                        Rp {{ number_format($rental->final_amount, 0, ',', '.') }}
                                    </td>
                                    <td>
                                        @php
                                            $statusClass = match($rental->status) {
                                                'pending' => 'warning',
                                                'active' => 'primary',
                                                'completed' => 'success',
                                                'cancelled' => 'secondary',
                                                'overdue' => 'danger',
                                                default => 'secondary'
                                            };
                                            $statusText = match($rental->status) {
                                                'pending' => 'Pending',
                                                'active' => 'Aktif',
                                                'completed' => 'Selesai',
                                                'cancelled' => 'Dibatalkan',
                                                'overdue' => 'Terlambat',
                                                default => ucfirst($rental->status)
                                            };
                                        @endphp
                                        <span class="badge bg-{{ $statusClass }}">{{ $statusText }}</span>
                                    </td>
                                    <td>
                                        <a href="{{ route('admin.rentals.show', $rental) }}" 
                                           class="btn btn-sm btn-outline-info" 
                                           title="Lihat Detail">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                    </td>
                                </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                    
                    @if($customer->rentals->count() > 10)
                        <div class="text-center mt-3">
                            <a href="{{ route('admin.rentals.index', ['search' => $customer->name]) }}" class="btn btn-outline-primary">
                                <i class="fas fa-list me-1"></i>
                                Lihat Semua Rental
                            </a>
                        </div>
                    @endif
                @else
                    <div class="text-center py-4">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">Customer belum pernah melakukan rental.</p>
                        <a href="{{ route('admin.rentals.create') }}" class="btn btn-primary">
                            <i class="fas fa-plus me-1"></i>
                            Buat Rental Baru
                        </a>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    Aksi Cepat
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.customers.edit', $customer) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-1"></i>
                        Edit Customer
                    </a>

                    <a href="{{ route('admin.rentals.create') }}?customer_id={{ $customer->id }}" class="btn btn-success">
                        <i class="fas fa-plus me-1"></i>
                        Buat Rental Baru
                    </a>



                    @if($customer->rentals->count() == 0)
                        <form method="POST" action="{{ route('admin.customers.destroy', $customer) }}" 
                              onsubmit="return confirm('Yakin ingin menghapus customer ini?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-1"></i>
                                Hapus Customer
                            </button>
                        </form>
                    @endif

                    <a href="{{ route('admin.customers.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Kembali ke Daftar
                    </a>
                </div>
            </div>
        </div>

        <!-- Customer Activity -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-clock me-2"></i>
                    Aktivitas Terakhir
                </h6>
            </div>
            <div class="card-body">
                @if($customer->rentals->count() > 0)
                    @php $lastRental = $customer->rentals->first(); @endphp
                    <div class="mb-3">
                        <strong>Rental Terakhir:</strong><br>
                        <small class="text-muted">{{ $lastRental->created_at->diffForHumans() }}</small><br>
                        <span class="badge bg-secondary">{{ $lastRental->rental_code }}</span>
                    </div>
                @endif
                
                <div class="mb-3">
                    <strong>Bergabung:</strong><br>
                    <small class="text-muted">{{ $customer->created_at->diffForHumans() }}</small>
                </div>
                
                <div>
                    <strong>Update Terakhir:</strong><br>
                    <small class="text-muted">{{ $customer->updated_at->diffForHumans() }}</small>
                </div>
            </div>
        </div>
    </div>
</div>


@endsection
