<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\PsUnit;
use App\Helpers\PsImageHelper;

class TestPsImages extends Command
{
    protected $signature = 'test:ps-images';
    protected $description = 'Test PS image mapping for all units';

    public function handle()
    {
        $this->info('Testing PS Image Mapping...');
        $this->newLine();

        // Get all unique PS types from database
        $psTypes = PsUnit::select('ps_type')->distinct()->pluck('ps_type');
        
        $this->info('PS Types found in database:');
        foreach ($psTypes as $type) {
            $this->line("- \"$type\"");
        }
        $this->newLine();

        // Test mapping for each type
        $this->info('Testing image mapping:');
        foreach ($psTypes as $type) {
            $image = PsImageHelper::getPsImage($type);
            $displayName = PsImageHelper::getPsDisplayName($type);
            $imageName = basename($image);
            
            $this->line("Type: \"$type\"");
            $this->line("  Display: $displayName");
            $this->line("  Image: $imageName");
            
            // Check if file exists
            $imagePath = public_path('images/ps-units/' . $imageName);
            if (file_exists($imagePath)) {
                $this->line("  Status: ✅ File exists");
            } else {
                $this->line("  Status: ❌ File missing");
            }
            $this->newLine();
        }

        // Test all PS units
        $this->info('Testing all PS units:');
        $units = PsUnit::all();
        foreach ($units as $unit) {
            $image = $unit->ps_image;
            $displayName = $unit->ps_display_name;
            $imageName = basename($image);
            
            $this->line("Unit: {$unit->unit_code} ({$unit->ps_type})");
            $this->line("  Display: $displayName");
            $this->line("  Image: $imageName");
        }

        $this->info('Test completed!');
    }
}
