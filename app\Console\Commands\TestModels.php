<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Models\PsUnit;
use App\Models\Customer;
use App\Models\Rental;
use App\Models\User;

class TestModels extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:test-models';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test all models and their methods';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Models...');

        try {
            // Test PsUnit model
            $psUnits = PsUnit::count();
            $this->info("Total PS Units: {$psUnits}");

            $availableUnits = PsUnit::available()->count();
            $this->info("Available PS Units: {$availableUnits}");

            if ($psUnits > 0) {
                $firstUnit = PsUnit::first();
                $this->info("First PS Unit: {$firstUnit->getAttribute('unit_code')}");
                $this->info("Status: {$firstUnit->getAttribute('status')}");
                $this->info("Is Available: " . ($firstUnit->isAvailable() ? 'Yes' : 'No'));
            }

            // Test Customer model
            $customers = Customer::count();
            $this->info("Total Customers: {$customers}");

            if ($customers > 0) {
                $firstCustomer = Customer::first();
                $this->info("First Customer: {$firstCustomer->getAttribute('name')}");
                $this->info("Is Active: " . ($firstCustomer->isActive() ? 'Yes' : 'No'));
            }

            // Test User model
            $users = User::count();
            $this->info("Total Users: {$users}");

            // Test Rental model
            $rentals = Rental::count();
            $this->info("Total Rentals: {$rentals}");

            $this->info('All models are working correctly!');

        } catch (\Exception $e) {
            $this->error('Error testing models: ' . $e->getMessage());
            $this->error('Stack trace: ' . $e->getTraceAsString());
        }
    }
}
