<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Customer;
use App\Models\PsUnit;
use App\Models\Rental;
use Illuminate\Support\Facades\Hash;
use Carbon\Carbon;

class SampleDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create sample customers
        $customers = [
            [
                'name' => 'Ahmad Rizki',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'phone' => '081234567890',
                'address' => 'Jl. Merdeka No. 123, Jakarta',
                'birth_date' => '1995-05-15',
                'gender' => 'male',
                'id_card_number' => '3171051505950001',
                'status' => 'active',
            ],
            [
                'name' => 'Siti Nurhaliza',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'phone' => '081234567891',
                'address' => 'J<PERSON>. <PERSON>man No. 456, Jakarta',
                'birth_date' => '1992-08-20',
                'gender' => 'female',
                'id_card_number' => '3171052008920002',
                'status' => 'active',
            ],
            [
                'name' => 'Budi Santoso',
                'email' => '<EMAIL>',
                'password' => Hash::make('password123'),
                'phone' => '081234567892',
                'address' => 'Jl. Thamrin No. 789, Jakarta',
                'birth_date' => '1990-12-10',
                'gender' => 'male',
                'id_card_number' => '3171051012900003',
                'status' => 'active',
            ],
        ];

        foreach ($customers as $customerData) {
            Customer::create($customerData);
        }

        // Create sample PS Units
        $psUnits = [
            [
                'unit_code' => 'PS001',
                'ps_type' => 'PlayStation 5',
                'brand' => 'Sony',
                'description' => 'PS5 Standard Edition dengan controller DualSense',
                'price_per_hour' => 15000,
                'price_per_day' => 100000,
                'status' => 'available',
                'accessories' => ['Controller DualSense', 'Kabel HDMI', 'Kabel Power'],
                'condition' => 'excellent',
                'purchase_date' => '2023-01-15',
            ],
            [
                'unit_code' => 'PS002',
                'ps_type' => 'PlayStation 5',
                'brand' => 'Sony',
                'description' => 'PS5 Standard Edition dengan 2 controller',
                'price_per_hour' => 18000,
                'price_per_day' => 120000,
                'status' => 'rented',
                'accessories' => ['2x Controller DualSense', 'Kabel HDMI', 'Kabel Power'],
                'condition' => 'excellent',
                'purchase_date' => '2023-02-20',
            ],
            [
                'unit_code' => 'PS003',
                'ps_type' => 'PlayStation 4',
                'brand' => 'Sony',
                'description' => 'PS4 Slim 1TB dengan controller',
                'price_per_hour' => 10000,
                'price_per_day' => 70000,
                'status' => 'available',
                'accessories' => ['Controller DualShock 4', 'Kabel HDMI', 'Kabel Power'],
                'condition' => 'good',
                'purchase_date' => '2022-06-10',
            ],
            [
                'unit_code' => 'PS004',
                'ps_type' => 'PlayStation 4',
                'brand' => 'Sony',
                'description' => 'PS4 Pro 1TB dengan 2 controller',
                'price_per_hour' => 12000,
                'price_per_day' => 85000,
                'status' => 'available',
                'accessories' => ['2x Controller DualShock 4', 'Kabel HDMI', 'Kabel Power'],
                'condition' => 'good',
                'purchase_date' => '2022-08-15',
            ],
            [
                'unit_code' => 'PS005',
                'ps_type' => 'PlayStation 5',
                'brand' => 'Sony',
                'description' => 'PS5 Digital Edition',
                'price_per_hour' => 14000,
                'price_per_day' => 95000,
                'status' => 'maintenance',
                'accessories' => ['Controller DualSense', 'Kabel HDMI', 'Kabel Power'],
                'condition' => 'fair',
                'purchase_date' => '2023-03-10',
            ],
        ];

        foreach ($psUnits as $unitData) {
            PsUnit::create($unitData);
        }

        // Create sample rentals
        $rentals = [
            [
                'rental_code' => 'RNT0001',
                'customer_id' => 1,
                'ps_unit_id' => 2,
                'start_datetime' => Carbon::now()->subDays(2),
                'end_datetime' => Carbon::now()->addDays(1),
                'rental_type' => 'daily',
                'duration' => 3,
                'price_per_unit' => 120000,
                'total_price' => 360000,
                'deposit' => 72000,
                'final_amount' => 360000,
                'status' => 'active',
                'payment_status' => 'paid',
                'notes' => 'Customer meminta 2 controller',
            ],
            [
                'rental_code' => 'RNT0002',
                'customer_id' => 2,
                'ps_unit_id' => 1,
                'start_datetime' => Carbon::now()->subDays(5),
                'end_datetime' => Carbon::now()->subDays(3),
                'actual_return_datetime' => Carbon::now()->subDays(3),
                'rental_type' => 'daily',
                'duration' => 2,
                'price_per_unit' => 100000,
                'total_price' => 200000,
                'deposit' => 40000,
                'final_amount' => 200000,
                'status' => 'completed',
                'payment_status' => 'paid',
                'notes' => 'Rental untuk acara ulang tahun',
            ],
            [
                'rental_code' => 'RNT0003',
                'customer_id' => 3,
                'ps_unit_id' => 3,
                'start_datetime' => Carbon::now()->subHours(8),
                'end_datetime' => Carbon::now()->subHours(2),
                'rental_type' => 'hourly',
                'duration' => 6,
                'price_per_unit' => 10000,
                'total_price' => 60000,
                'deposit' => 12000,
                'penalty' => 20000,
                'final_amount' => 80000,
                'status' => 'overdue',
                'payment_status' => 'partial',
                'notes' => 'Customer terlambat mengembalikan',
            ],
            [
                'rental_code' => 'RNT0004',
                'customer_id' => 1,
                'ps_unit_id' => 4,
                'start_datetime' => Carbon::now()->addDays(1),
                'end_datetime' => Carbon::now()->addDays(3),
                'rental_type' => 'daily',
                'duration' => 2,
                'price_per_unit' => 85000,
                'total_price' => 170000,
                'deposit' => 34000,
                'final_amount' => 170000,
                'status' => 'pending',
                'payment_status' => 'unpaid',
                'notes' => 'Booking untuk weekend',
            ],
            [
                'rental_code' => 'RNT0005',
                'customer_id' => 2,
                'ps_unit_id' => 1,
                'start_datetime' => Carbon::now()->subDays(10),
                'end_datetime' => Carbon::now()->subDays(8),
                'actual_return_datetime' => Carbon::now()->subDays(8),
                'rental_type' => 'hourly',
                'duration' => 8,
                'price_per_unit' => 15000,
                'total_price' => 120000,
                'deposit' => 24000,
                'final_amount' => 120000,
                'status' => 'completed',
                'payment_status' => 'paid',
                'notes' => 'Rental untuk turnamen game',
            ],
        ];

        foreach ($rentals as $rentalData) {
            Rental::create($rentalData);
        }

        $this->command->info('Sample data created successfully!');
        $this->command->info('Customers: ' . Customer::count());
        $this->command->info('PS Units: ' . PsUnit::count());
        $this->command->info('Rentals: ' . Rental::count());
    }
}
