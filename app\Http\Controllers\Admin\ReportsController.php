<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Rental;
use App\Models\Customer;
use App\Models\PsUnit;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Barryvdh\DomPDF\Facade\Pdf;

class ReportsController extends Controller
{
    /**
     * Tampilkan halaman utama reports
     */
    public function index(Request $request)
    {
        try {
            // Validasi input tanggal
            $request->validate([
                'start_date' => 'nullable|date',
                'end_date' => 'nullable|date|after_or_equal:start_date',
            ]);

            // Default periode: bulan ini
            $startDate = $request->get('start_date', now()->startOfMonth()->format('Y-m-d'));
            $endDate = $request->get('end_date', now()->endOfMonth()->format('Y-m-d'));

            // Konversi ke Carbon untuk perhitungan
            $start = Carbon::parse($startDate)->startOfDay();
            $end = Carbon::parse($endDate)->endOfDay();

        // Statistik Umum
        $generalStats = [
            'total_rentals' => Rental::whereBetween('created_at', [$start, $end])->count(),
            'total_revenue' => Rental::whereBetween('created_at', [$start, $end])
                                   ->where('payment_status', 'paid')
                                   ->sum('final_amount'),
            'total_customers' => Customer::whereBetween('created_at', [$start, $end])->count(),
            'active_rentals' => Rental::where('status', 'active')->count(),
            'overdue_rentals' => Rental::where('status', 'overdue')->count(),
            'total_penalty' => Rental::whereBetween('created_at', [$start, $end])->sum('penalty'),
        ];

        // Rental per Status
        $rentalByStatus = Rental::whereBetween('created_at', [$start, $end])
                               ->select('status', DB::raw('count(*) as total'))
                               ->groupBy('status')
                               ->get()
                               ->pluck('total', 'status')
                               ->toArray();

        // Revenue per Bulan (12 bulan terakhir)
        $monthlyRevenue = [];
        for ($i = 11; $i >= 0; $i--) {
            $month = now()->subMonths($i);
            $revenue = Rental::whereYear('created_at', $month->year)
                           ->whereMonth('created_at', $month->month)
                           ->where('payment_status', 'paid')
                           ->sum('final_amount');
            $monthlyRevenue[] = [
                'month' => $month->format('M Y'),
                'revenue' => $revenue
            ];
        }

        // PS Unit Terpopuler
        $popularUnits = Rental::whereBetween('created_at', [$start, $end])
                            ->select('ps_unit_id', DB::raw('count(*) as rental_count'))
                            ->with('psUnit')
                            ->groupBy('ps_unit_id')
                            ->orderBy('rental_count', 'desc')
                            ->limit(5)
                            ->get();

        // Customer Terbaik
        $topCustomers = Rental::whereBetween('created_at', [$start, $end])
                            ->where('payment_status', 'paid')
                            ->select('customer_id', 
                                   DB::raw('count(*) as rental_count'),
                                   DB::raw('sum(final_amount) as total_spent'))
                            ->with('customer')
                            ->groupBy('customer_id')
                            ->orderBy('total_spent', 'desc')
                            ->limit(5)
                            ->get();

        // Rental per Hari berdasarkan periode yang dipilih
        $dailyRentals = [];
        $periodStart = Carbon::parse($startDate);
        $periodEnd = Carbon::parse($endDate);
        $daysDiff = $periodStart->diffInDays($periodEnd);

        // Jika periode lebih dari 30 hari, group by week
        if ($daysDiff > 30) {
            // Group by week
            $currentWeek = $periodStart->copy()->startOfWeek();
            while ($currentWeek->lte($periodEnd)) {
                $weekEnd = $currentWeek->copy()->endOfWeek();
                if ($weekEnd->gt($periodEnd)) {
                    $weekEnd = $periodEnd->copy();
                }

                $count = Rental::whereBetween('created_at', [$currentWeek, $weekEnd])->count();
                $revenue = Rental::whereBetween('created_at', [$currentWeek, $weekEnd])
                                ->where('payment_status', 'paid')
                                ->sum('final_amount');

                $dailyRentals[] = [
                    'date' => $currentWeek->format('d M') . ' - ' . $weekEnd->format('d M'),
                    'count' => $count,
                    'revenue' => $revenue
                ];

                $currentWeek->addWeek();
            }
        } else {
            // Group by day
            $currentDate = $periodStart->copy();
            while ($currentDate->lte($periodEnd)) {
                $count = Rental::whereDate('created_at', $currentDate)->count();
                $revenue = Rental::whereDate('created_at', $currentDate)
                                ->where('payment_status', 'paid')
                                ->sum('final_amount');

                $dailyRentals[] = [
                    'date' => $currentDate->format('d M'),
                    'count' => $count,
                    'revenue' => $revenue
                ];

                $currentDate->addDay();
            }
        }

            return view('admin.reports.index', compact(
                'generalStats',
                'rentalByStatus',
                'monthlyRevenue',
                'popularUnits',
                'topCustomers',
                'dailyRentals',
                'startDate',
                'endDate'
            ));

        } catch (\Exception $e) {
            Log::error('Error in reports index: ' . $e->getMessage());

            // Fallback data
            $generalStats = [
                'total_rentals' => 0,
                'total_revenue' => 0,
                'total_customers' => 0,
                'active_rentals' => 0,
                'overdue_rentals' => 0,
                'total_penalty' => 0,
            ];

            $rentalByStatus = [];
            $monthlyRevenue = [];
            $popularUnits = collect();
            $topCustomers = collect();
            $dailyRentals = [];
            $startDate = now()->startOfMonth()->format('Y-m-d');
            $endDate = now()->endOfMonth()->format('Y-m-d');

            return view('admin.reports.index', compact(
                'generalStats',
                'rentalByStatus',
                'monthlyRevenue',
                'popularUnits',
                'topCustomers',
                'dailyRentals',
                'startDate',
                'endDate'
            ))->with('error', 'Terjadi kesalahan saat memuat dashboard laporan.');
        }
    }

    /**
     * Export laporan ke Excel/PDF
     */
    public function export(Request $request)
    {
        try {
            $startDate = $request->get('start_date', now()->startOfMonth()->format('Y-m-d'));
            $endDate = $request->get('end_date', now()->endOfMonth()->format('Y-m-d'));

            $start = Carbon::parse($startDate)->startOfDay();
            $end = Carbon::parse($endDate)->endOfDay();

            // Prepare data for PDF export
            $data = [
                'period' => Carbon::parse($startDate)->format('d/m/Y') . ' - ' . Carbon::parse($endDate)->format('d/m/Y'),
                'rentals' => Rental::with(['customer', 'psUnit'])
                                  ->whereBetween('created_at', [$start, $end])
                                  ->orderBy('created_at', 'desc')
                                  ->get(),
                'summary' => [
                    'total_rentals' => Rental::whereBetween('created_at', [$start, $end])->count(),
                    'total_revenue' => Rental::whereBetween('created_at', [$start, $end])
                                           ->where('payment_status', 'paid')
                                           ->sum('final_amount'),
                    'total_penalty' => Rental::whereBetween('created_at', [$start, $end])->sum('penalty'),
                ]
            ];

            // Generate PDF
            $pdf = Pdf::loadView('admin.reports.pdf.report', $data);
            $pdf->setPaper('A4', 'portrait');
            $pdf->setOptions([
                'isHtml5ParserEnabled' => true,
                'isPhpEnabled' => true,
                'defaultFont' => 'Arial',
                'chroot' => public_path(),
                'isRemoteEnabled' => true
            ]);

            $filename = 'Laporan-Rental-' . Carbon::parse($startDate)->format('d-m-Y') . '-sampai-' . Carbon::parse($endDate)->format('d-m-Y') . '.pdf';

            return $pdf->download($filename);

        } catch (\Exception $e) {
            Log::error('Error in export PDF: ' . $e->getMessage());

            return redirect()->route('admin.reports.index')
                            ->with('error', 'Terjadi kesalahan saat export laporan PDF: ' . $e->getMessage());
        }
    }

    /**
     * Laporan detail rental
     */
    public function rentals(Request $request)
    {
        try {
            // Validasi input tanggal
            $request->validate([
                'start_date' => 'nullable|date',
                'end_date' => 'nullable|date|after_or_equal:start_date',
                'status' => 'nullable|in:pending,active,completed,cancelled,overdue',
                'payment_status' => 'nullable|in:unpaid,partial,paid,refunded',
            ]);

            $query = Rental::with(['customer', 'psUnit']);

            // Filter berdasarkan periode
            if ($request->filled('start_date')) {
                $query->whereDate('created_at', '>=', $request->get('start_date'));
            }
            if ($request->filled('end_date')) {
                $query->whereDate('created_at', '<=', $request->get('end_date'));
            }

            // Filter berdasarkan status
            if ($request->filled('status')) {
                $query->where('status', $request->get('status'));
            }

            // Filter berdasarkan payment status
            if ($request->filled('payment_status')) {
                $query->where('payment_status', $request->get('payment_status'));
            }

            $rentals = $query->orderBy('created_at', 'desc')->paginate(20);

            // Buat query terpisah untuk statistik (clone query sebelum pagination)
            $statsQuery = Rental::query();

            // Apply same filters untuk stats
            if ($request->filled('start_date')) {
                $statsQuery->whereDate('created_at', '>=', $request->get('start_date'));
            }
            if ($request->filled('end_date')) {
                $statsQuery->whereDate('created_at', '<=', $request->get('end_date'));
            }
            if ($request->filled('status')) {
                $statsQuery->where('status', $request->get('status'));
            }
            if ($request->filled('payment_status')) {
                $statsQuery->where('payment_status', $request->get('payment_status'));
            }

            // Statistik untuk periode yang dipilih
            $stats = [
                'total_rentals' => $statsQuery->count(),
                'total_revenue' => $statsQuery->where('payment_status', 'paid')->sum('final_amount'),
                'total_penalty' => $statsQuery->sum('penalty'),
                'avg_rental_value' => $statsQuery->where('payment_status', 'paid')->avg('final_amount') ?: 0,
            ];

            // Check if export PDF is requested
            if ($request->get('export') === 'pdf') {
                return $this->exportRentalsPdf($request, $rentals, $stats);
            }

            return view('admin.reports.rentals', compact('rentals', 'stats'));

        } catch (\Exception $e) {
            Log::error('Error in rentals report: ' . $e->getMessage());
            return redirect()->route('admin.reports.index')
                            ->withErrors(['error' => 'Terjadi kesalahan saat memuat laporan rental.']);
        }
    }

    /**
     * Export laporan rental ke PDF
     */
    private function exportRentalsPdf(Request $request, $rentals, $stats)
    {
        try {
            // Prepare filters info
            $filters = [];
            if ($request->filled('start_date')) {
                $filters['Tanggal Mulai'] = Carbon::parse($request->get('start_date'))->format('d/m/Y');
            }
            if ($request->filled('end_date')) {
                $filters['Tanggal Selesai'] = Carbon::parse($request->get('end_date'))->format('d/m/Y');
            }
            if ($request->filled('status')) {
                $filters['Status'] = ucfirst($request->get('status'));
            }
            if ($request->filled('payment_status')) {
                $filters['Status Payment'] = ucfirst($request->get('payment_status'));
            }

            $data = [
                'rentals' => $rentals,
                'stats' => $stats,
                'filters' => $filters,
                'period' => count($filters) > 0 ? 'Dengan Filter' : 'Semua Data'
            ];

            $pdf = Pdf::loadView('admin.reports.pdf.rentals', $data);
            $pdf->setPaper('A4', 'landscape'); // Landscape untuk tabel yang lebar

            $filename = 'laporan-detail-rental-' . now()->format('Y-m-d-H-i-s') . '.pdf';

            return $pdf->download($filename);

        } catch (\Exception $e) {
            Log::error('Error in export rentals PDF: ' . $e->getMessage());

            return redirect()->route('admin.reports.rentals')
                            ->with('error', 'Terjadi kesalahan saat export PDF: ' . $e->getMessage());
        }
    }

    /**
     * Laporan customer
     */
    public function customers(Request $request)
    {
        try {
            // Validasi input
            $request->validate([
                'start_date' => 'nullable|date',
                'end_date' => 'nullable|date|after_or_equal:start_date',
                'status' => 'nullable|in:active,inactive,banned',
            ]);

            $query = Customer::withCount(['rentals'])
                            ->with(['rentals' => function($q) {
                                $q->where('payment_status', 'paid');
                            }]);

            // Filter berdasarkan status
            if ($request->filled('status')) {
                $query->where('status', $request->get('status'));
            }

            // Filter berdasarkan periode bergabung
            if ($request->filled('start_date')) {
                $query->whereDate('created_at', '>=', $request->get('start_date'));
            }
            if ($request->filled('end_date')) {
                $query->whereDate('created_at', '<=', $request->get('end_date'));
            }

            $customers = $query->orderBy('created_at', 'desc')->paginate(20);

            // Hitung total spent untuk setiap customer
            foreach ($customers as $customer) {
                $customer->total_spent = $customer->rentals->sum('final_amount');
            }

            // Statistik
            $stats = [
                'total_customers' => Customer::count(),
                'active_customers' => Customer::where('status', 'active')->count(),
                'customers_with_rentals' => Customer::has('rentals')->count(),
                'avg_rentals_per_customer' => round(Rental::count() / max(Customer::count(), 1), 2),
            ];

            return view('admin.reports.customers', compact('customers', 'stats'));

        } catch (\Exception $e) {
            Log::error('Error in customers report: ' . $e->getMessage());
            return redirect()->route('admin.reports.index')
                            ->withErrors(['error' => 'Terjadi kesalahan saat memuat laporan customer.']);
        }
    }

    /**
     * Laporan PS Units
     */
    public function psUnits(Request $request)
    {
        try {
            // Validasi input
            $request->validate([
                'status' => 'nullable|in:available,rented,maintenance,broken',
                'ps_type' => 'nullable|string|max:255',
            ]);

            $query = PsUnit::withCount(['rentals'])
                          ->with(['rentals' => function($q) {
                              $q->where('payment_status', 'paid');
                          }]);

            // Filter berdasarkan status
            if ($request->filled('status')) {
                $query->where('status', $request->get('status'));
            }

            // Filter berdasarkan tipe PS
            if ($request->filled('ps_type')) {
                $query->where('ps_type', 'like', "%{$request->get('ps_type')}%");
            }

            $psUnits = $query->orderBy('rentals_count', 'desc')->paginate(20);

            // Hitung revenue untuk setiap unit
            foreach ($psUnits as $unit) {
                $unit->total_revenue = $unit->rentals->sum('final_amount');
            }

            // Statistik
            $stats = [
                'total_units' => PsUnit::count(),
                'available_units' => PsUnit::where('status', 'available')->count(),
                'rented_units' => PsUnit::where('status', 'rented')->count(),
                'maintenance_units' => PsUnit::where('status', 'maintenance')->count(),
            ];

            return view('admin.reports.ps-units', compact('psUnits', 'stats'));

        } catch (\Exception $e) {
            Log::error('Error in PS units report: ' . $e->getMessage());
            return redirect()->route('admin.reports.index')
                            ->withErrors(['error' => 'Terjadi kesalahan saat memuat laporan PS units.']);
        }
    }
}
