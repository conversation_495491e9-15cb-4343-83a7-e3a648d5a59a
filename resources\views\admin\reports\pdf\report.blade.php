<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laporan Rental PS - {{ $period }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 12px;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 3px solid #007bff;
            padding-bottom: 20px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 25px;
            border-radius: 8px;
        }

        .header h1 {
            color: #007bff;
            margin: 0;
            font-size: 28px;
            font-weight: bold;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }

        .header h2 {
            color: #495057;
            margin: 8px 0 0 0;
            font-size: 18px;
            font-weight: 500;
        }

        .header .company-info {
            margin-top: 10px;
            font-size: 11px;
            color: #6c757d;
        }
        
        .period {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
            margin-bottom: 25px;
            font-weight: bold;
            font-size: 14px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .summary {
            display: table;
            width: 100%;
            margin-bottom: 30px;
        }
        
        .summary-item {
            display: table-cell;
            width: 33.33%;
            text-align: center;
            padding: 20px 15px;
            background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
            border: 2px solid #e9ecef;
            box-shadow: 0 2px 4px rgba(0,0,0,0.05);
        }
        
        .summary-item:first-child {
            border-radius: 5px 0 0 5px;
        }
        
        .summary-item:last-child {
            border-radius: 0 5px 5px 0;
        }
        
        .summary-value {
            font-size: 20px;
            font-weight: bold;
            color: #007bff;
            display: block;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
        }

        .summary-label {
            font-size: 12px;
            color: #495057;
            margin-top: 8px;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }
        
        .section-title {
            background: #007bff;
            color: white;
            padding: 10px;
            margin: 20px 0 10px 0;
            font-weight: bold;
            font-size: 14px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        th, td {
            border: 1px solid #dee2e6;
            padding: 8px;
            text-align: left;
        }
        
        th {
            background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
            color: white;
            font-weight: bold;
            font-size: 11px;
            text-align: center;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.2);
        }

        td {
            font-size: 10px;
            vertical-align: middle;
        }

        tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        tbody tr:hover {
            background-color: #e9ecef;
        }
        
        .text-center {
            text-align: center;
        }
        
        .text-right {
            text-align: right;
        }
        
        .status-badge {
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 9px;
            font-weight: bold;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-completed {
            background: #cce5ff;
            color: #004085;
        }
        
        .status-cancelled {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-overdue {
            background: #f5c6cb;
            color: #721c24;
        }
        
        .payment-paid {
            background: #d4edda;
            color: #155724;
        }
        
        .payment-unpaid {
            background: #f8d7da;
            color: #721c24;
        }
        
        .payment-partial {
            background: #fff3cd;
            color: #856404;
        }
        
        .footer {
            margin-top: 40px;
            text-align: center;
            font-size: 10px;
            color: #6c757d;
            border-top: 2px solid #007bff;
            padding-top: 15px;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
        }
        
        .page-break {
            page-break-before: always;
        }

        @media print {
            body {
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <h1>🎮 RENTAL PLAYSTATION</h1>
        <h2>Laporan Rental & Analisis</h2>
        <div class="company-info">
            Sistem Manajemen Rental PlayStation | Generated by Admin
        </div>
    </div>
    
    <!-- Period -->
    <div class="period">
        Periode: {{ $period }}
    </div>
    
    <!-- Summary -->
    <div class="summary">
        <div class="summary-item">
            <span class="summary-value">{{ $summary['total_rentals'] }}</span>
            <div class="summary-label">Total Rental</div>
        </div>
        <div class="summary-item">
            <span class="summary-value">Rp {{ number_format($summary['total_revenue'], 0, ',', '.') }}</span>
            <div class="summary-label">Total Revenue</div>
        </div>
        <div class="summary-item">
            <span class="summary-value">Rp {{ number_format($summary['total_penalty'], 0, ',', '.') }}</span>
            <div class="summary-label">Total Denda</div>
        </div>
    </div>
    
    <!-- Rental Details -->
    <div class="section-title">DETAIL RENTAL</div>
    
    @if($rentals->count() > 0)
        <table>
            <thead>
                <tr>
                    <th width="8%">No</th>
                    <th width="15%">Tanggal</th>
                    <th width="20%">Customer</th>
                    <th width="15%">PS Unit</th>
                    <th width="12%">Durasi</th>
                    <th width="12%">Total</th>
                    <th width="10%">Status</th>
                    <th width="8%">Payment</th>
                </tr>
            </thead>
            <tbody>
                @foreach($rentals as $index => $rental)
                    <tr>
                        <td class="text-center">{{ $index + 1 }}</td>
                        <td>{{ $rental->created_at ? \Carbon\Carbon::parse($rental->created_at)->format('d/m/Y H:i') : 'N/A' }}</td>
                        <td>{{ $rental->customer ? $rental->customer->name : 'N/A' }}</td>
                        <td>{{ $rental->psUnit ? $rental->psUnit->name : 'N/A' }}</td>
                        <td class="text-center">{{ $rental->duration }} jam</td>
                        <td class="text-right">Rp {{ number_format($rental->final_amount, 0, ',', '.') }}</td>
                        <td class="text-center">
                            <span class="status-badge status-{{ $rental->status }}">
                                {{ ucfirst($rental->status) }}
                            </span>
                        </td>
                        <td class="text-center">
                            <span class="status-badge payment-{{ $rental->payment_status }}">
                                {{ ucfirst($rental->payment_status) }}
                            </span>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    @else
        <div style="text-align: center; padding: 20px; color: #666;">
            Tidak ada data rental untuk periode ini.
        </div>
    @endif
    
    <!-- Footer -->
    <div class="footer">
        <p><strong>📅 Laporan digenerate pada:</strong> {{ now()->format('d/m/Y H:i:s') }}</p>
        <p><strong>🏢 Rental PlayStation Management System</strong></p>
        <p style="margin-top: 8px; font-size: 9px; color: #868e96;">
            Dokumen ini dibuat secara otomatis oleh sistem dan sah tanpa tanda tangan
        </p>
    </div>
</body>
</html>
