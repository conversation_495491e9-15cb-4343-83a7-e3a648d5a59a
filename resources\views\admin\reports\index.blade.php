@extends('layouts.app')

@section('title', 'Laporan - Rental PS Admin')
@section('page-title', 'Laporan & Analisis')

@section('content')
<div class="row mb-4">
    <div class="col-md-6">
        <h4>
            <i class="fas fa-chart-bar me-2"></i>
            Dashboard Laporan
        </h4>
    </div>
    <div class="col-md-6 text-end">
        <div class="btn-group" role="group">
            <a href="{{ route('admin.reports.rentals') }}" class="btn btn-outline-primary report-nav-btn">
                <i class="fas fa-calendar-alt me-1"></i>
                Laporan <PERSON>tal
            </a>
            <a href="{{ route('admin.reports.ps-units') }}" class="btn btn-outline-primary report-nav-btn">
                <i class="fas fa-gamepad me-1"></i>
                Laporan PS Units
            </a>
        </div>
    </div>
</div>

<!-- Filter Periode -->
<div class="card mb-4">
    <div class="card-header">
        <h6 class="mb-0">
            <i class="fas fa-calendar me-2"></i>
            Filter Periode
        </h6>
    </div>
    <div class="card-body">
        <form method="GET" action="{{ route('admin.reports.index') }}">
            <div class="row">
                <div class="col-md-4">
                    <label class="form-label">Tanggal Mulai</label>
                    <input type="date" name="start_date" class="form-control" value="{{ $startDate }}">
                </div>
                <div class="col-md-4">
                    <label class="form-label">Tanggal Selesai</label>
                    <input type="date" name="end_date" class="form-control" value="{{ $endDate }}">
                </div>
                <div class="col-md-4 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i>
                        Filter
                    </button>
                    <a href="{{ route('admin.reports.export') }}?start_date={{ $startDate }}&end_date={{ $endDate }}&type=pdf"
                       class="btn btn-danger">
                        <i class="fas fa-file-pdf me-1"></i>
                        Export PDF
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Statistik Umum -->
<div class="row mb-4">
    <div class="col-md-2 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-calendar-check fa-2x mb-2 text-primary"></i>
                <h4>{{ $generalStats['total_rentals'] }}</h4>
                <small class="text-muted">Total Rental</small>
            </div>
        </div>
    </div>
    <div class="col-md-2 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-money-bill-wave fa-2x mb-2 text-success"></i>
                <h5>Rp {{ number_format($generalStats['total_revenue'], 0, ',', '.') }}</h5>
                <small class="text-muted">Total Revenue</small>
            </div>
        </div>
    </div>
    <div class="col-md-2 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-users fa-2x mb-2 text-info"></i>
                <h4>{{ $generalStats['total_customers'] }}</h4>
                <small class="text-muted">Customer Baru</small>
            </div>
        </div>
    </div>
    <div class="col-md-2 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-play fa-2x mb-2 text-warning"></i>
                <h4>{{ $generalStats['active_rentals'] }}</h4>
                <small class="text-muted">Rental Aktif</small>
            </div>
        </div>
    </div>
    <div class="col-md-2 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-exclamation-triangle fa-2x mb-2 text-danger"></i>
                <h4>{{ $generalStats['overdue_rentals'] }}</h4>
                <small class="text-muted">Terlambat</small>
            </div>
        </div>
    </div>
    <div class="col-md-2 mb-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-gavel fa-2x mb-2 text-secondary"></i>
                <h5>Rp {{ number_format($generalStats['total_penalty'], 0, ',', '.') }}</h5>
                <small class="text-muted">Total Denda</small>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- Chart Revenue Bulanan -->
    <div class="col-md-8 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    Revenue 12 Bulan Terakhir
                </h6>
            </div>
            <div class="card-body">
                <canvas id="revenueChart" height="100"></canvas>
            </div>
        </div>
    </div>

    <!-- Chart Rental per Status -->
    <div class="col-md-4 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-pie me-2"></i>
                    Rental per Status
                </h6>
            </div>
            <div class="card-body">
                <canvas id="statusChart" height="200"></canvas>
            </div>
        </div>
    </div>
</div>

<div class="row">
    <!-- PS Unit Terpopuler -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-gamepad me-2"></i>
                    PS Unit Terpopuler
                </h6>
            </div>
            <div class="card-body">
                @if($popularUnits->count() > 0)
                    @foreach($popularUnits as $unit)
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <strong>{{ $unit->psUnit->unit_code }}</strong><br>
                                <small class="text-muted">{{ $unit->psUnit->ps_type }}</small>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-primary">{{ $unit->rental_count }} rental</span>
                            </div>
                        </div>
                        @if(!$loop->last)<hr>@endif
                    @endforeach
                @else
                    <div class="text-center text-muted">
                        <i class="fas fa-info-circle fa-2x mb-2"></i>
                        <p>Tidak ada data untuk periode ini</p>
                    </div>
                @endif
            </div>
        </div>
    </div>

    <!-- Customer Terbaik -->
    <div class="col-md-6 mb-4">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-crown me-2"></i>
                    Customer Terbaik
                </h6>
            </div>
            <div class="card-body">
                @if($topCustomers->count() > 0)
                    @foreach($topCustomers as $customer)
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div>
                                <strong>{{ $customer->customer->name }}</strong><br>
                                <small class="text-muted">{{ $customer->rental_count }} rental</small>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-success">Rp {{ number_format($customer->total_spent, 0, ',', '.') }}</span>
                            </div>
                        </div>
                        @if(!$loop->last)<hr>@endif
                    @endforeach
                @else
                    <div class="text-center text-muted">
                        <i class="fas fa-info-circle fa-2x mb-2"></i>
                        <p>Tidak ada data untuk periode ini</p>
                    </div>
                @endif
            </div>
        </div>
    </div>
</div>

<!-- Chart Rental Harian -->
<div class="row">
    <div class="col-12">
        <div class="card shadow-sm" style="border: none; border-radius: 15px; overflow: hidden;">
            <div class="card-header" style="background: #60B5FF; border: none; padding: 20px;">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-1 text-white fw-bold">
                            <i class="fas fa-chart-line me-2"></i>
                            Grafik Rental Harian
                        </h5>
                        <p class="mb-0 text-white-50 small">Analisis pola rental per hari</p>
                    </div>
                    <div class="text-end">
                        <span class="badge bg-white px-3 py-2 rounded-pill" style="color: #60B5FF;">
                            <i class="fas fa-calendar-alt me-1"></i>
                            {{ $startDate }} - {{ $endDate }}
                        </span>
                    </div>
                </div>
            </div>
            <div class="card-body" style="padding: 30px; background: #f8f9ff;">
                <div class="row mb-3">
                    <div class="col-md-3">
                        <div class="d-flex align-items-center">
                            <div class="rounded-circle p-2 me-3" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; background: #60B5FF;">
                                <i class="fas fa-calendar-check text-white"></i>
                            </div>
                            <div>
                                <h6 class="mb-0">{{ array_sum(array_column($dailyRentals, 'count')) }}</h6>
                                <small class="text-muted">Total Rental</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-center">
                            <div class="rounded-circle p-2 me-3" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; background: #60B5FF;">
                                <i class="fas fa-chart-line text-white"></i>
                            </div>
                            <div>
                                <h6 class="mb-0">{{ round(array_sum(array_column($dailyRentals, 'count')) / max(count($dailyRentals), 1), 1) }}</h6>
                                <small class="text-muted">Rata-rata/Hari</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-center">
                            <div class="rounded-circle p-2 me-3" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; background: #60B5FF;">
                                <i class="fas fa-arrow-up text-white"></i>
                            </div>
                            <div>
                                <h6 class="mb-0">{{ max(array_column($dailyRentals, 'count')) }}</h6>
                                <small class="text-muted">Puncak Harian</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex align-items-center">
                            <div class="rounded-circle p-2 me-3" style="width: 40px; height: 40px; display: flex; align-items: center; justify-content: center; background: #60B5FF;">
                                <i class="fas fa-percentage text-white"></i>
                            </div>
                            <div>
                                <h6 class="mb-0">{{ count(array_filter(array_column($dailyRentals, 'count'), function($c) { return $c > 0; })) }}</h6>
                                <small class="text-muted">Hari Aktif</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div style="position: relative; height: 400px; background: white; border-radius: 12px; padding: 20px; box-shadow: 0 2px 10px rgba(96, 181, 255, 0.1);">
                    <canvas id="dailyChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Revenue Chart
    const revenueCtx = document.getElementById('revenueChart').getContext('2d');
    new Chart(revenueCtx, {
        type: 'line',
        data: {
            labels: {!! json_encode(array_column($monthlyRevenue, 'month')) !!},
            datasets: [{
                label: 'Revenue (Rp)',
                data: {!! json_encode(array_column($monthlyRevenue, 'revenue')) !!},
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                tension: 0.4,
                fill: true
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        callback: function(value) {
                            return 'Rp ' + value.toLocaleString('id-ID');
                        }
                    }
                }
            }
        }
    });

    // Status Chart
    const statusCtx = document.getElementById('statusChart').getContext('2d');
    const statusData = @json($rentalByStatus);
    new Chart(statusCtx, {
        type: 'doughnut',
        data: {
            labels: Object.keys(statusData).map(key => key.charAt(0).toUpperCase() + key.slice(1)),
            datasets: [{
                data: Object.values(statusData),
                backgroundColor: [
                    '#ffc107', // pending - warning
                    '#0d6efd', // active - primary
                    '#198754', // completed - success
                    '#6c757d', // cancelled - secondary
                    '#dc3545'  // overdue - danger
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });

    // Daily Rental Chart - Beautiful Modern Design
    const dailyCtx = document.getElementById('dailyChart').getContext('2d');
    const dailyData = {!! json_encode(array_column($dailyRentals, 'count')) !!};
    const dailyLabels = {!! json_encode(array_column($dailyRentals, 'date')) !!};
    const maxValue = Math.max(...dailyData);

    // Solid Blue Theme - No Gradient
    const primaryBlue = '#60B5FF';
    const primaryBlueRgba = 'rgba(96, 181, 255, 0.8)';
    const primaryBlueHover = 'rgba(96, 181, 255, 1)';
    const primaryBlueLight = 'rgba(96, 181, 255, 0.3)';

    new Chart(dailyCtx, {
        type: 'bar',
        data: {
            labels: dailyLabels,
            datasets: [{
                label: 'Jumlah Rental',
                data: dailyData,
                backgroundColor: primaryBlueRgba,
                borderColor: primaryBlue,
                borderWidth: 2,
                borderRadius: {
                    topLeft: 8,
                    topRight: 8,
                    bottomLeft: 0,
                    bottomRight: 0
                },
                borderSkipped: false,
                hoverBackgroundColor: primaryBlueHover,
                hoverBorderColor: primaryBlue,
                hoverBorderWidth: 3,
                barThickness: 'flex',
                maxBarThickness: 60
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    enabled: true,
                    backgroundColor: 'rgba(255, 255, 255, 0.95)',
                    titleColor: '#333',
                    bodyColor: '#666',
                    borderColor: primaryBlueLight,
                    borderWidth: 1,
                    cornerRadius: 12,
                    displayColors: false,
                    titleFont: {
                        size: 14,
                        weight: 'bold'
                    },
                    bodyFont: {
                        size: 13
                    },
                    padding: 12,
                    callbacks: {
                        title: function(context) {
                            return '📅 ' + context[0].label;
                        },
                        label: function(context) {
                            const count = context.parsed.y;
                            return `🎮 ${count} rental${count !== 1 ? 's' : ''}`;
                        },
                        afterLabel: function(context) {
                            const count = context.parsed.y;
                            if (count === maxValue && maxValue > 0) {
                                return '🏆 Hari tersibuk!';
                            } else if (count === 0) {
                                return '😴 Tidak ada rental';
                            } else if (count >= maxValue * 0.8) {
                                return '🔥 Hari sibuk';
                            }
                            return '';
                        }
                    }
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1,
                        color: '#8e9aaf',
                        font: {
                            size: 12,
                            family: 'Inter, sans-serif'
                        },
                        padding: 10,
                        callback: function(value) {
                            return value + (value === 1 ? ' rental' : ' rentals');
                        }
                    },
                    grid: {
                        color: 'rgba(142, 154, 175, 0.1)',
                        drawBorder: false,
                        lineWidth: 1
                    },
                    border: {
                        display: false
                    }
                },
                x: {
                    ticks: {
                        color: '#8e9aaf',
                        font: {
                            size: 11,
                            family: 'Inter, sans-serif'
                        },
                        maxRotation: 45,
                        minRotation: 0,
                        padding: 10
                    },
                    grid: {
                        display: false
                    },
                    border: {
                        display: false
                    }
                }
            },
            animation: {
                duration: 1500,
                easing: 'easeOutCubic',
                delay: function(context) {
                    return context.dataIndex * 100;
                }
            },
            interaction: {
                intersect: false,
                mode: 'index'
            },
            elements: {
                bar: {
                    borderWidth: 2,
                    borderSkipped: false
                }
            }
        }
    });
});
</script>
@endsection
