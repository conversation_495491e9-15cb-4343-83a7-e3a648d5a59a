<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Login - Rental PS Admin</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-blue: #60B5FF;
            --light-blue: #E3F2FD;
            --dark-blue: #1976D2;
            --very-light-blue: #F5F9FF;
            --text-dark: #2C3E50;
            --gradient-primary: linear-gradient(135deg, #60B5FF 0%, #1976D2 100%);
            --gradient-secondary: linear-gradient(135deg, #E3F2FD 0%, #F5F9FF 100%);
        }

        body {
            background: #ffffff;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        /* Subtle Background Pattern */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-image:
                radial-gradient(circle at 25% 25%, rgba(96, 181, 255, 0.05) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(25, 118, 210, 0.05) 0%, transparent 50%);
            z-index: 1;
        }

        .login-container {
            position: relative;
            z-index: 10;
            max-width: 420px;
            width: 100%;
            margin: 0 20px;
        }

        .login-card {
            background: #ffffff;
            border-radius: 20px;
            box-shadow:
                0 20px 40px rgba(96, 181, 255, 0.1),
                0 1px 3px rgba(0, 0, 0, 0.05);
            border: 1px solid rgba(96, 181, 255, 0.1);
            overflow: hidden;
            animation: slideInUp 0.8s ease-out;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .login-header {
            background: #ffffff;
            color: var(--dark-blue);
            padding: 2.5rem 2rem 1.5rem;
            text-align: center;
            position: relative;
            border-bottom: 1px solid rgba(96, 181, 255, 0.1);
        }

        .login-header .icon-container {
            display: inline-block;
            padding: 20px;
            background: var(--very-light-blue);
            border-radius: 50%;
            margin-bottom: 1.5rem;
            color: var(--primary-blue);
            box-shadow: 0 8px 25px rgba(96, 181, 255, 0.15);
        }

        .login-header h3 {
            font-weight: 700;
            font-size: 1.75rem;
            margin-bottom: 0.5rem;
            color: var(--dark-blue);
        }

        .login-header p {
            color: #6c757d;
            font-size: 0.95rem;
            margin-bottom: 0;
        }

        .login-body {
            padding: 2rem;
            background: #ffffff;
        }

        .form-floating {
            position: relative;
            margin-bottom: 1.5rem;
        }

        .form-label {
            font-weight: 600;
            color: #374151;
            margin-bottom: 0.5rem;
            font-size: 0.9rem;
        }

        .form-control {
            border-radius: 12px;
            border: 1px solid #e5e7eb;
            padding: 0.875rem 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
            background: #ffffff;
        }

        .form-control:focus {
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 3px rgba(96, 181, 255, 0.1);
            outline: none;
        }

        .input-group {
            margin-bottom: 1.25rem;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            border-radius: 12px;
            overflow: hidden;
        }

        .input-group-text {
            background: #f9fafb;
            border: 1px solid #e5e7eb;
            border-right: none;
            border-radius: 12px 0 0 12px;
            padding: 0.875rem 1rem;
            color: var(--primary-blue);
        }

        .form-control.with-icon {
            border-left: none;
            border-radius: 0 12px 12px 0;
        }

        .btn-login {
            background: linear-gradient(135deg, var(--primary-blue), var(--dark-blue));
            border: none;
            border-radius: 12px;
            padding: 0.875rem 2rem;
            font-weight: 600;
            font-size: 1rem;
            transition: all 0.3s ease;
            color: white;
            margin-top: 0.5rem;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(96, 181, 255, 0.3);
        }

        .form-check {
            margin: 1.25rem 0;
        }

        .form-check-input:checked {
            background-color: var(--primary-blue);
            border-color: var(--primary-blue);
        }

        .form-check-label {
            color: #6c757d;
            font-size: 0.9rem;
        }

        .form-check-input:checked {
            background-color: var(--primary-blue);
            border-color: var(--primary-blue);
        }



        /* Responsive Design */
        @media (max-width: 768px) {
            .login-container {
                margin: 0 16px;
            }

            .login-header {
                padding: 2rem 1.5rem 1.25rem;
            }

            .login-header h3 {
                font-size: 1.5rem;
            }

            .login-body {
                padding: 1.5rem;
            }

            .input-group {
                margin-bottom: 1rem;
            }
        }

        /* Loading Animation */
        .btn-login.loading {
            pointer-events: none;
        }

        .btn-login.loading::after {
            content: '';
            position: absolute;
            width: 20px;
            height: 20px;
            margin: auto;
            border: 2px solid transparent;
            border-top-color: #ffffff;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="icon-container">
                    <i class="fas fa-gamepad fa-3x"></i>
                </div>
                <h3 class="mb-2">Rental PS Admin</h3>
                <p class="mb-0">Selamat datang! Silakan login untuk melanjutkan</p>
            </div>
        
        <div class="login-body">
            <form method="POST" action="{{ route('login') }}">
                @csrf
                
                <div class="mb-3">
                    <label for="email" class="form-label">Email</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-envelope text-muted"></i>
                        </span>
                        <input type="email" 
                               class="form-control with-icon @error('email') is-invalid @enderror" 
                               id="email" 
                               name="email" 
                               value="{{ old('email') }}" 
                               placeholder="Masukkan email Anda"
                               required>
                    </div>
                    @error('email')
                        <div class="text-danger small mt-1">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="mb-3">
                    <label for="password" class="form-label">Password</label>
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-lock text-muted"></i>
                        </span>
                        <input type="password" 
                               class="form-control with-icon @error('password') is-invalid @enderror" 
                               id="password" 
                               name="password" 
                               placeholder="Masukkan password Anda"
                               required>
                    </div>
                    @error('password')
                        <div class="text-danger small mt-1">{{ $message }}</div>
                    @enderror
                </div>
                
                <div class="mb-3 form-check">
                    <input type="checkbox" class="form-check-input" id="remember" name="remember">
                    <label class="form-check-label" for="remember">
                        Ingat saya
                    </label>
                </div>
                
                <button type="submit" class="btn btn-primary btn-login w-100">
                    <i class="fas fa-sign-in-alt me-2"></i>
                    Login
                </button>
            </form>
            

        </div>
    </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Form submission with loading state
        document.querySelector('form').addEventListener('submit', function(e) {
            const submitBtn = document.querySelector('.btn-login');
            const btnText = submitBtn.innerHTML;

            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Masuk...';
            submitBtn.disabled = true;

            // If there's an error, restore the button after 3 seconds
            setTimeout(() => {
                if (submitBtn.disabled) {
                    submitBtn.innerHTML = btnText;
                    submitBtn.disabled = false;
                }
            }, 3000);
        });

        // Enhanced focus effects
        document.querySelectorAll('.input-group').forEach(group => {
            const input = group.querySelector('.form-control');

            input.addEventListener('focus', () => {
                group.style.transform = 'translateY(-2px)';
                group.style.boxShadow = '0 8px 32px rgba(96, 181, 255, 0.12)';
            });

            input.addEventListener('blur', () => {
                group.style.transform = 'translateY(0)';
                group.style.boxShadow = '0 4px 16px rgba(0, 0, 0, 0.04)';
            });
        });
    </script>
</body>
</html>
