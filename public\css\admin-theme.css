/* ===== RENTAL PS ADMIN - SOLID BLUE THEME ===== */

:root {
    --primary-blue: #60B5FF;      /* Main Blue */
    --light-blue: #E3F2FD;       /* Light Blue */
    --dark-blue: #1976D2;        /* Dark Blue */
    --very-light-blue: #F5F9FF;  /* Very Light Blue */
    --text-dark: #2C3E50;        /* Dark Text */
    --text-light: #ffffff;       /* White Text */
}



/* ===== GLOBAL OVERRIDES ===== */
.btn-primary {
    background: var(--primary-blue) !important;
    border: none !important;
    color: white !important;
    font-weight: 600;
    transition: all 0.3s ease;
    border-radius: 10px;
    padding: 12px 25px;
}

.btn-primary:hover {
    background: var(--dark-blue) !important;
    transform: translateY(-3px);
    box-shadow: 0 8px 20px rgba(96, 181, 255, 0.4);
}

.btn-success {
    background: var(--primary-blue) !important;
    border: none !important;
    color: white !important;
    font-weight: 600;
}

.btn-info {
    background: var(--primary-blue) !important;
    border: none !important;
    color: white !important;
    font-weight: 600;
}

.btn-warning {
    background: #ffc107 !important;
    border: none !important;
    color: #333 !important;
    font-weight: 600;
}

.btn-danger {
    background: #FFEBEE !important;
    border: 2px solid #F44336 !important;
    color: #F44336 !important;
    font-weight: 600;
}

/* ===== BOOTSTRAP OVERRIDES ===== */
.bg-primary {
    background: var(--primary-blue) !important;
    color: var(--text-light) !important;
}

.bg-success {
    background: var(--dark-blue) !important;
    color: var(--text-light) !important;
}

.bg-info {
    background: var(--light-blue) !important;
    color: black;
}

.bg-warning {
    background: var(--very-light-blue) !important;
    color: var(--text-dark) !important;
    border: 2px solid var(--primary-blue);
}

.bg-danger {
    background: #FFEBEE !important;
    color: #F44336 !important;
    border: 2px solid #F44336;
}

.text-primary {
    color: var(--primary-blue) !important;
}

/* ===== BADGES ===== */
.badge.bg-primary {
    background: #60B5FF !important;
    color: white !important;
    border: none !important;
}

.badge.bg-success {
    background: #28a745 !important;
    color: white !important;
    border: none !important;
}

.badge.bg-info {
    background: #17a2b8 !important;
    color: white !important;
    border: none !important;
}

.badge.bg-warning {
    background: #ffc107 !important;
    color: #212529 !important;
    border: none !important;
}

.badge.bg-danger {
    background: #dc3545 !important;
    color: white !important;
    border: none !important;
}

.badge.bg-secondary {
    background: #6c757d !important;
    color: white !important;
    border: none !important;
}

/* ===== ALERTS ===== */
.alert-success {
    background: var(--very-light-blue) !important;
    border: 1px solid var(--primary-blue) !important;
    color: var(--text-blue) !important;
}

.alert-danger {
    background: var(--white-blue) !important;
    border: 1px solid var(--light-blue) !important;
    color: var(--text-blue) !important;
}

.alert-warning {
    background: var(--very-light-blue) !important;
    border: 1px solid var(--primary-blue) !important;
    color: var(--text-blue) !important;
}

.alert-info {
    background: var(--white-blue) !important;
    border: 1px solid var(--light-blue) !important;
    color: var(--text-blue) !important;
}

/* ===== TABLES ===== */
.table thead th {
    background: var(--primary-blue) !important;
    color: white !important;
    border-bottom: 2px solid var(--primary-blue) !important;
    padding: 12px 8px !important;
    font-size: 0.9rem !important;
    font-weight: 600 !important;
}

.table tbody td {
    color: #333 !important;
    padding: 12px 8px !important;
    vertical-align: middle !important;
}

.table tbody td.text-primary {
    color: #333 !important;
}

.table-hover tbody tr:hover {
    background-color: #f8f9fa !important;
}

/* ===== ACTION COLUMN FIXES ===== */
.table th:last-child,
.table td:last-child {
    width: 120px !important;
    max-width: 120px !important;
    text-align: center !important;
}

.table .btn-group {
    display: flex !important;
    gap: 4px !important;
    justify-content: center !important;
    flex-wrap: nowrap !important;
}

.table .btn-sm {
    padding: 4px 8px !important;
    font-size: 0.75rem !important;
    border-radius: 6px !important;
    min-width: 32px !important;
    height: 32px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.table .btn-outline-secondary {
    border-color: #dee2e6 !important;
    color: #6c757d !important;
}

.table .btn-outline-secondary:hover {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
    color: white !important;
}

.table .btn-outline-warning {
    border-color: #ffc107 !important;
    color: #ffc107 !important;
}

.table .btn-outline-warning:hover {
    background-color: #ffc107 !important;
    border-color: #ffc107 !important;
    color: #212529 !important;
}

.table .btn-outline-danger {
    border-color: #dc3545 !important;
    color: #dc3545 !important;
}

.table .btn-outline-danger:hover {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    color: white !important;
}

/* ===== FORMS ===== */
.form-control:focus {
    border-color: var(--primary-blue) !important;
    box-shadow: 0 0 0 0.2rem rgba(135, 206, 235, 0.25) !important;
}

.form-select:focus {
    border-color: var(--primary-blue) !important;
    box-shadow: 0 0 0 0.2rem rgba(135, 206, 235, 0.25) !important;
}

/* ===== PAGINATION ===== */
.page-link {
    color: var(--dark-blue) !important;
    border-color: var(--light-blue) !important;
}

.page-link:hover {
    color: var(--text-blue) !important;
    background-color: var(--very-light-blue) !important;
    border-color: var(--primary-blue) !important;
}

.page-item.active .page-link {
    background-color: var(--primary-blue) !important;
    border-color: var(--primary-blue) !important;
    color: var(--text-blue) !important;
}

/* ===== NAVBAR ===== */
.navbar-light .navbar-brand {
    color: var(--dark-blue) !important;
    font-weight: bold;
}

.navbar-light .navbar-nav .nav-link {
    color: var(--text-blue) !important;
}

/* ===== CUSTOM ANIMATIONS ===== */
@keyframes blueGlow {
    0% { box-shadow: 0 0 5px rgba(135, 206, 235, 0.3); }
    50% { box-shadow: 0 0 20px rgba(135, 206, 235, 0.6); }
    100% { box-shadow: 0 0 5px rgba(135, 206, 235, 0.3); }
}

.card:hover {
    animation: blueGlow 2s infinite;
}

/* ===== ENHANCED CARD STYLES ===== */
.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(96, 181, 255, 0.1);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(96, 181, 255, 0.2);
    animation: none; /* Override the previous glow animation */
}

.card-header {
    background: #60B5FF;
    border-bottom: 1px solid rgba(96, 181, 255, 0.3);
    border-radius: 15px 15px 0 0 !important;
    padding: 1.25rem 1.5rem;
    color: white;
}

.card-header h5, .card-header h6 {
    color: white;
    font-weight: 700;
    margin-bottom: 0;
}

/* ===== ENHANCED BUTTONS ===== */
.btn {
    border-radius: 10px;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
}

.btn:active {
    transform: translateY(0);
}

/* ===== ENHANCED NAVBAR ===== */
.navbar {
    backdrop-filter: blur(20px);
    background: rgba(255, 255, 255, 0.95) !important;
    border-bottom: 1px solid rgba(96, 181, 255, 0.1);
    box-shadow: 0 2px 20px rgba(96, 181, 255, 0.1);
}

.navbar-brand {
    font-weight: 800;
    font-size: 1.5rem;
    color: var(--dark-blue) !important;
    text-shadow: 0 2px 4px rgba(96, 181, 255, 0.1);
}

/* ===== ENHANCED SIDEBAR ===== */
.sidebar {
    background: linear-gradient(180deg, var(--primary-blue) 0%, var(--dark-blue) 100%);
    box-shadow: 2px 0 20px rgba(96, 181, 255, 0.2);
    border-radius: 0 20px 20px 0;
}

.sidebar .nav-link {
    color: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    margin: 0.25rem 0.5rem;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.sidebar .nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    transform: translateX(5px);
}

.sidebar .nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* ===== ENHANCED STATS CARDS ===== */
.stats-card {
    background: linear-gradient(135deg, var(--very-light-blue) 0%, rgba(255, 255, 255, 0.9) 100%);
    border: 1px solid rgba(96, 181, 255, 0.2);
    border-radius: 15px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--gradient-primary);
}

.stats-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(96, 181, 255, 0.2);
}

.stats-card h3 {
    color: black;
    font-weight: 800;
    font-size: 2.5rem;
    margin-bottom: 0.5rem;
    text-shadow: none;
}

.stats-card p {
    color: var(--text-dark);
    font-weight: 600;
    margin-bottom: 0;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.9rem;
}

/* ===== ENHANCED ALERTS ===== */
.alert {
    border: none;
    border-radius: 15px;
    padding: 1.25rem 1.5rem;
    border-left: 4px solid;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    width: 50px;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1));
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.alert-success {
    background: rgba(40, 167, 69, 0.1);
    color: #155724;
    border-left-color: #28a745;
}

.alert-danger {
    background: rgba(220, 53, 69, 0.1);
    color: #721c24;
    border-left-color: #dc3545;
}

.alert-warning {
    background: rgba(255, 193, 7, 0.1);
    color: #856404;
    border-left-color: #ffc107;
}

.alert-info {
    background: rgba(96, 181, 255, 0.1);
    color: var(--dark-blue);
    border-left-color: var(--primary-blue);
}

/* ===== RESPONSIVE ADJUSTMENTS ===== */
@media (max-width: 768px) {
    .sidebar {
        background: linear-gradient(180deg, var(--primary-blue) 0%, var(--dark-blue) 100%);
        border-radius: 0;
    }

    .stats-card h3 {
        font-size: 2rem;
    }

    .card {
        margin-bottom: 1rem;
    }

    .btn {
        padding: 0.6rem 1.2rem;
        font-size: 0.9rem;
    }
}

/* ===== LOADING ANIMATIONS ===== */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.6s ease-out;
}

/* ===== TABLE IMPROVEMENTS ===== */
.table-responsive {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(96, 181, 255, 0.1);
}

.table th {
    white-space: nowrap;
    font-size: 0.85rem;
    padding: 12px 8px !important;
    font-weight: 600 !important;
}

.table td {
    font-size: 0.9rem;
    padding: 12px 8px !important;
    vertical-align: middle !important;
}

/* Actions column - consistent across all tables */
.table th:last-child,
.table td:last-child {
    width: 120px !important;
    max-width: 120px !important;
    text-align: center !important;
    padding: 8px !important;
}

/* Badge improvements */
.badge {
    font-size: 0.75rem !important;
    padding: 4px 8px !important;
    border-radius: 6px !important;
    font-weight: 500 !important;
}

/* Button group improvements */
.btn-group {
    display: inline-flex !important;
    gap: 1px !important;
    border-radius: 4px !important;
}

.btn-group .btn {
    border-radius: 4px !important;
    margin: 0 !important;
    padding: 4px 6px !important;
    font-size: 0.7rem !important;
    line-height: 1 !important;
    min-width: 28px !important;
    height: 28px !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
    border-width: 1px !important;
}

/* Outline button improvements - No hover animations */
.btn-outline-secondary {
    border-color: #6c757d !important;
    color: #6c757d !important;
    background-color: transparent !important;
    transition: none !important;
}

.btn-outline-secondary:hover,
.btn-outline-secondary:focus,
.btn-outline-secondary:active {
    background-color: transparent !important;
    border-color: #6c757d !important;
    color: #6c757d !important;
    box-shadow: none !important;
}

.btn-outline-warning {
    border-color: #ffc107 !important;
    color: #ffc107 !important;
    background-color: transparent !important;
    transition: none !important;
}

.btn-outline-warning:hover,
.btn-outline-warning:focus,
.btn-outline-warning:active {
    background-color: transparent !important;
    border-color: #ffc107 !important;
    color: #ffc107 !important;
    box-shadow: none !important;
}

.btn-outline-danger {
    border-color: #dc3545 !important;
    color: #dc3545 !important;
    background-color: transparent !important;
    transition: none !important;
}

.btn-outline-danger:hover,
.btn-outline-danger:focus,
.btn-outline-danger:active {
    background-color: transparent !important;
    border-color: #dc3545 !important;
    color: #dc3545 !important;
    box-shadow: none !important;
}

/* Disabled button styling */
.btn:disabled {
    opacity: 0.5 !important;
    cursor: not-allowed !important;
}

/* ===== REPORT NAVIGATION BUTTONS ===== */
.report-nav-btn {
    border: 2px solid var(--primary-blue) !important;
    color: var(--primary-blue) !important;
    background-color: white !important;
    transition: all 0.2s ease !important;
    padding: 12px 20px !important;
    font-size: 0.95rem !important;
    font-weight: 600 !important;
    border-radius: 12px !important;
    margin: 0 4px !important;
    box-shadow: 0 2px 8px rgba(96, 181, 255, 0.1) !important;
    text-decoration: none !important;
    display: inline-flex !important;
    align-items: center !important;
    justify-content: center !important;
}

.report-nav-btn:hover {
    background-color: var(--primary-blue) !important;
    border-color: var(--primary-blue) !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 15px rgba(96, 181, 255, 0.3) !important;
    text-decoration: none !important;
}

.report-nav-btn:focus,
.report-nav-btn:active {
    background-color: var(--dark-blue) !important;
    border-color: var(--dark-blue) !important;
    color: white !important;
    box-shadow: 0 4px 15px rgba(96, 181, 255, 0.3) !important;
    text-decoration: none !important;
}

.report-nav-btn i {
    margin-right: 8px !important;
    font-size: 1rem !important;
}

/* Remove btn-group specific styling for report buttons */
.btn-group .report-nav-btn {
    border-radius: 12px !important;
    margin: 0 4px !important;
}

.btn-group .report-nav-btn:first-child,
.btn-group .report-nav-btn:last-child {
    border-radius: 12px !important;
}

.btn-group .report-nav-btn:not(:last-child) {
    border-right: 2px solid var(--primary-blue) !important;
}

/* ===== RENTAL TIME ICONS ===== */
.fa-play.text-success {
    color: #28a745 !important;
}

.fa-stop.text-danger {
    color: #dc3545 !important;
}

/* ===== CARD STATISTICS NUMBERS ===== */
.card h3,
.card h4,
.card h5 {
    color: black !important;
}

.stats-card h3,
.stats-card h4,
.stats-card h5 {
    color: black !important;
}

/* ===== CUSTOM SCROLLBAR ===== */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--very-light-blue);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary-blue);
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--dark-blue);
}
