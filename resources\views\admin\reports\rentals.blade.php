@extends('layouts.app')

@section('title', 'Laporan <PERSON> - Rental PS Admin')
@section('page-title', 'Laporan Rental')

@section('content')
<div class="row mb-4">
    <div class="col-md-6">
        <h4>
            <i class="fas fa-calendar-alt me-2"></i>
            Laporan Detail Rental
        </h4>
    </div>
    <div class="col-md-6 text-end">
        <a href="{{ route('admin.reports.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>
            Kembali ke Dashboard
        </a>
    </div>
</div>

<!-- Statistik Ringkas -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card text-center" style="background: white; border: 1px solid #e5e7eb;">
            <div class="card-body">
                <i class="fas fa-list fa-2x mb-2" style="color: #667eea;"></i>
                <h4 class="text-dark">{{ number_format($stats['total_rentals']) }}</h4>
                <small class="text-muted">Total Rental</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card text-center" style="background: white; border: 1px solid #e5e7eb;">
            <div class="card-body">
                <i class="fas fa-money-bill-wave fa-2x mb-2" style="color: #11998e;"></i>
                <h5 class="text-dark">Rp {{ number_format($stats['total_revenue'], 0, ',', '.') }}</h5>
                <small class="text-muted">Total Revenue</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card text-center" style="background: white; border: 1px solid #e5e7eb;">
            <div class="card-body">
                <i class="fas fa-gavel fa-2x mb-2" style="color: #ff6b6b;"></i>
                <h5 class="text-dark">Rp {{ number_format($stats['total_penalty'], 0, ',', '.') }}</h5>
                <small class="text-muted">Total Denda</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card text-center" style="background: white; border: 1px solid #e5e7eb;">
            <div class="card-body">
                <i class="fas fa-chart-line fa-2x mb-2" style="color: #ffa726;"></i>
                <h5 class="text-dark">Rp {{ number_format($stats['avg_rental_value'], 0, ',', '.') }}</h5>
                <small class="text-muted">Rata-rata Nilai</small>
            </div>
        </div>
    </div>
</div>

<!-- Filter -->
<div class="card mb-4">
    <div class="card-header">
        <h6 class="mb-0">
            <i class="fas fa-filter me-2"></i>
            Filter Laporan
        </h6>
    </div>
    <div class="card-body">
        <form method="GET" action="{{ route('admin.reports.rentals') }}">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <label class="form-label">Tanggal Mulai</label>
                    <input type="date" name="start_date" class="form-control" value="{{ request('start_date') }}">
                </div>
                <div class="col-md-3 mb-3">
                    <label class="form-label">Tanggal Selesai</label>
                    <input type="date" name="end_date" class="form-control" value="{{ request('end_date') }}">
                </div>
                <div class="col-md-3 mb-3">
                    <label class="form-label">Status Rental</label>
                    <select name="status" class="form-select">
                        <option value="">Semua Status</option>
                        <option value="pending" {{ request('status') == 'pending' ? 'selected' : '' }}>Pending</option>
                        <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Aktif</option>
                        <option value="completed" {{ request('status') == 'completed' ? 'selected' : '' }}>Selesai</option>
                        <option value="overdue" {{ request('status') == 'overdue' ? 'selected' : '' }}>Terlambat</option>
                        <option value="cancelled" {{ request('status') == 'cancelled' ? 'selected' : '' }}>Dibatalkan</option>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label class="form-label">Status Pembayaran</label>
                    <select name="payment_status" class="form-select">
                        <option value="">Semua Status</option>
                        <option value="unpaid" {{ request('payment_status') == 'unpaid' ? 'selected' : '' }}>Belum Bayar</option>
                        <option value="partial" {{ request('payment_status') == 'partial' ? 'selected' : '' }}>Sebagian</option>
                        <option value="paid" {{ request('payment_status') == 'paid' ? 'selected' : '' }}>Lunas</option>
                        <option value="refunded" {{ request('payment_status') == 'refunded' ? 'selected' : '' }}>Dikembalikan</option>
                    </select>
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i>
                        Filter
                    </button>
                    <a href="{{ route('admin.reports.rentals') }}" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-times me-1"></i>
                        Reset
                    </a>
                    <button type="submit" name="export" value="pdf" class="btn btn-danger">
                        <i class="fas fa-file-pdf me-1"></i>
                        Export PDF
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Tabel Rental -->
<div class="card">
    <div class="card-header">
        <h6 class="mb-0">
            <i class="fas fa-table me-2"></i>
            Detail Rental ({{ $rentals->total() }} total)
        </h6>
    </div>
    <div class="card-body">
        @if($rentals->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Kode Rental</th>
                            <th>Customer</th>
                            <th>PS Unit</th>
                            <th>Tanggal</th>
                            <th>Durasi</th>
                            <th>Harga</th>
                            <th>Denda</th>
                            <th>Total</th>
                            <th>Status</th>
                            <th>Pembayaran</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($rentals as $rental)
                        <tr>
                            <td>
                                <a href="{{ route('admin.rentals.show', $rental) }}" class="text-decoration-none">
                                    <strong class="text-primary">{{ $rental->rental_code }}</strong>
                                </a>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ $rental->customer->name }}</strong><br>
                                    <small class="text-muted">{{ $rental->customer->phone }}</small>
                                </div>
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ $rental->psUnit->unit_code }}</span><br>
                                <small>{{ $rental->psUnit->ps_type }}</small>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ $rental->start_datetime->format('d/m/Y') }}</strong><br>
                                    <small class="text-muted">{{ $rental->start_datetime->format('H:i') }} - {{ $rental->end_datetime->format('H:i') }}</small>
                                </div>
                            </td>
                            <td>
                                {{ $rental->duration }} {{ $rental->rental_type === 'hourly' ? 'jam' : 'hari' }}
                            </td>
                            <td>
                                Rp {{ number_format($rental->total_price, 0, ',', '.') }}
                            </td>
                            <td>
                                @if($rental->penalty > 0)
                                    <span class="text-danger">Rp {{ number_format($rental->penalty, 0, ',', '.') }}</span>
                                @else
                                    <span class="text-muted">-</span>
                                @endif
                            </td>
                            <td>
                                <strong>Rp {{ number_format($rental->final_amount, 0, ',', '.') }}</strong>
                            </td>
                            <td>
                                @php
                                    $statusClass = match($rental->status) {
                                        'pending' => 'warning',
                                        'active' => 'primary',
                                        'completed' => 'success',
                                        'cancelled' => 'secondary',
                                        'overdue' => 'danger',
                                        default => 'secondary'
                                    };
                                    $statusText = match($rental->status) {
                                        'pending' => 'Pending',
                                        'active' => 'Aktif',
                                        'completed' => 'Selesai',
                                        'cancelled' => 'Dibatalkan',
                                        'overdue' => 'Terlambat',
                                        default => ucfirst($rental->status)
                                    };
                                @endphp
                                <span class="badge bg-{{ $statusClass }}">{{ $statusText }}</span>
                            </td>
                            <td>
                                @php
                                    $paymentClass = match($rental->payment_status) {
                                        'unpaid' => 'danger',
                                        'partial' => 'warning',
                                        'paid' => 'success',
                                        'refunded' => 'info',
                                        default => 'secondary'
                                    };
                                    $paymentText = match($rental->payment_status) {
                                        'unpaid' => 'Belum Bayar',
                                        'partial' => 'Sebagian',
                                        'paid' => 'Lunas',
                                        'refunded' => 'Dikembalikan',
                                        default => ucfirst($rental->payment_status)
                                    };
                                @endphp
                                <span class="badge bg-{{ $paymentClass }}">{{ $paymentText }}</span>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $rentals->appends(request()->query())->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Tidak ada data rental</h5>
                <p class="text-muted">Tidak ada rental yang sesuai dengan filter yang dipilih.</p>
                <a href="{{ route('admin.reports.rentals') }}" class="btn btn-primary">
                    <i class="fas fa-refresh me-1"></i>
                    Reset Filter
                </a>
            </div>
        @endif
    </div>
</div>
@endsection
