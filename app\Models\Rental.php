<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Builder;

class Rental extends Model
{
    protected $fillable = [
        'rental_code',
        'customer_id',
        'ps_unit_id',
        'start_datetime',
        'end_datetime',
        'actual_return_datetime',
        'rental_type',
        'duration',
        'price_per_unit',
        'total_price',
        'deposit',
        'penalty',
        'final_amount',
        'status',
        'payment_status',
        'notes',
    ];

    protected $casts = [
        'start_datetime' => 'datetime',
        'end_datetime' => 'datetime',
        'actual_return_datetime' => 'datetime',
        'price_per_unit' => 'decimal:2',
        'total_price' => 'decimal:2',
        'deposit' => 'decimal:2',
        'penalty' => 'decimal:2',
        'final_amount' => 'decimal:2',
    ];

    // Relationship dengan customer
    public function customer(): BelongsTo
    {
        return $this->belongsTo(Customer::class);
    }

    // Relationship dengan ps_unit
    public function psUnit(): BelongsTo
    {
        return $this->belongsTo(PsUnit::class);
    }

    // Scope untuk rental aktif
    public function scopeActive(Builder $query): Builder
    {
        return $query->where('status', 'active');
    }

    // Scope untuk rental yang terlambat
    public function scopeOverdue(Builder $query): Builder
    {
        return $query->where('status', 'overdue');
    }

    // Check apakah rental terlambat
    public function isOverdue(): bool
    {
        return $this->getAttribute('status') === 'overdue' ||
               ($this->getAttribute('status') === 'active' && now() > $this->getAttribute('end_datetime'));
    }

    // Calculate penalty untuk keterlambatan
    public function calculatePenalty(): float
    {
        if (!$this->isOverdue()) {
            return 0;
        }

        $overdueDuration = now()->diffInHours($this->getAttribute('end_datetime'));
        $penaltyRate = $this->getAttribute('price_per_unit') * 0.1; // 10% dari harga per unit

        return $overdueDuration * $penaltyRate;
    }
}
