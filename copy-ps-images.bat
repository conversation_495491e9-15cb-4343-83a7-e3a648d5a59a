@echo off
echo ========================================
echo   PlayStation Images Setup Script
echo ========================================
echo.

REM Create directory if not exists
if not exist "public\images\ps-units" (
    mkdir "public\images\ps-units"
    echo Created directory: public\images\ps-units
)

echo.
echo Please copy your PlayStation images to public\images\ps-units\ with these names:
echo.
echo 1. ps4.jpg          - PlayStation 4 (black console image)
echo 2. ps4-pro.jpg      - PlayStation 4 Pro (blue box image)  
echo 3. ps4-slim.jpg     - PlayStation 4 Slim (green console image)
echo 4. ps5.jpg          - PlayStation 5 (white console image)
echo 5. ps5-pro.jpg      - PlayStation 5 Pro (blue PS5 Pro image)
echo.
echo Current files in ps-units directory:
dir "public\images\ps-units" /b 2>nul || echo No files found

echo.
echo After copying the images, press any key to test the application...
pause

echo.
echo Testing image accessibility...
for %%f in (ps4.jpg ps4-pro.jpg ps4-slim.jpg ps5.jpg ps5-pro.jpg) do (
    if exist "public\images\ps-units\%%f" (
        echo [OK] %%f found
    ) else (
        echo [MISSING] %%f not found
    )
)

echo.
echo Setup complete! You can now run: php artisan serve
pause
