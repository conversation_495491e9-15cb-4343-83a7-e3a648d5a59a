@extends('layouts.app')

@section('title', 'Edit Customer - Rental PS Admin')
@section('page-title', 'Edit Customer')

@section('content')
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-user-edit me-2"></i>
                    Edit Customer - {{ $customer->name }}
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" action="{{ route('admin.customers.update', $customer) }}">
                    @csrf
                    @method('PUT')
                    
                    <!-- Basic Information -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label"><PERSON><PERSON> <span class="text-danger">*</span></label>
                            <input type="text" name="name" id="name" 
                                   class="form-control @error('name') is-invalid @enderror" 
                                   value="{{ old('name', $customer->name) }}" required>
                            @error('name')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">Email <span class="text-danger">*</span></label>
                            <input type="email" name="email" id="email" 
                                   class="form-control @error('email') is-invalid @enderror" 
                                   value="{{ old('email', $customer->email) }}" required>
                            @error('email')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="password" class="form-label">Password Baru</label>
                            <input type="password" name="password" id="password" 
                                   class="form-control @error('password') is-invalid @enderror">
                            @error('password')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                            <div class="form-text">Kosongkan jika tidak ingin mengubah password</div>
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="phone" class="form-label">Nomor Telepon</label>
                            <input type="text" name="phone" id="phone" 
                                   class="form-control @error('phone') is-invalid @enderror" 
                                   value="{{ old('phone', $customer->phone) }}" placeholder="08xxxxxxxxxx">
                            @error('phone')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <!-- Personal Information -->
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="birth_date" class="form-label">Tanggal Lahir</label>
                            <input type="date" name="birth_date" id="birth_date" 
                                   class="form-control @error('birth_date') is-invalid @enderror" 
                                   value="{{ old('birth_date', $customer->birth_date?->format('Y-m-d')) }}">
                            @error('birth_date')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>

                        <div class="col-md-6 mb-3">
                            <label for="gender" class="form-label">Jenis Kelamin</label>
                            <select name="gender" id="gender" class="form-select @error('gender') is-invalid @enderror">
                                <option value="">Pilih Jenis Kelamin</option>
                                <option value="male" {{ old('gender', $customer->gender) == 'male' ? 'selected' : '' }}>Laki-laki</option>
                                <option value="female" {{ old('gender', $customer->gender) == 'female' ? 'selected' : '' }}>Perempuan</option>
                            </select>
                            @error('gender')
                                <div class="invalid-feedback">{{ $message }}</div>
                            @enderror
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="id_card_number" class="form-label">Nomor KTP</label>
                        <input type="text" name="id_card_number" id="id_card_number" 
                               class="form-control @error('id_card_number') is-invalid @enderror" 
                               value="{{ old('id_card_number', $customer->id_card_number) }}" placeholder="16 digit nomor KTP">
                        @error('id_card_number')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>

                    <div class="mb-3">
                        <label for="address" class="form-label">Alamat</label>
                        <textarea name="address" id="address" class="form-control @error('address') is-invalid @enderror" 
                                  rows="3" placeholder="Alamat lengkap">{{ old('address', $customer->address) }}</textarea>
                        @error('address')
                            <div class="invalid-feedback">{{ $message }}</div>
                        @enderror
                    </div>



                    <div class="d-flex justify-content-between">
                        <a href="{{ route('admin.customers.show', $customer) }}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-1"></i>
                            Kembali
                        </a>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-1"></i>
                            Update Customer
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Current Info -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Informasi Saat Ini
                </h6>
            </div>
            <div class="card-body">


                <div class="mb-3">
                    <label class="form-label">Total Rental:</label>
                    <div>
                        <span class="badge bg-info fs-6">{{ $customer->rentals->count() }}</span>
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">Bergabung:</label>
                    <div>{{ $customer->created_at->format('d F Y') }}</div>
                </div>

                <div class="mb-0">
                    <label class="form-label">Update Terakhir:</label>
                    <div>{{ $customer->updated_at->format('d F Y H:i') }}</div>
                </div>
            </div>
        </div>

        <!-- Rental Activity -->
        @if($customer->rentals->count() > 0)
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    Aktivitas Rental
                </h6>
            </div>
            <div class="card-body">
                @php
                    $activeRentals = $customer->rentals->where('status', 'active')->count();
                    $completedRentals = $customer->rentals->where('status', 'completed')->count();
                    $totalSpent = $customer->rentals->where('payment_status', 'paid')->sum('final_amount');
                @endphp
                
                <div class="row text-center">
                    <div class="col-6 mb-3">
                        <div class="border rounded p-2">
                            <h6 class="text-warning">{{ $activeRentals }}</h6>
                            <small class="text-muted">Aktif</small>
                        </div>
                    </div>
                    <div class="col-6 mb-3">
                        <div class="border rounded p-2">
                            <h6 class="text-success">{{ $completedRentals }}</h6>
                            <small class="text-muted">Selesai</small>
                        </div>
                    </div>
                </div>
                
                <div class="text-center">
                    <div class="border rounded p-2">
                        <h6 class="text-info">Rp {{ number_format($totalSpent, 0, ',', '.') }}</h6>
                        <small class="text-muted">Total Pengeluaran</small>
                    </div>
                </div>
            </div>
        </div>
        @endif



        <!-- Warning -->
        @if($customer->rentals->whereIn('status', ['active', 'pending'])->count() > 0)
        <div class="card mt-4 border-warning">
            <div class="card-header bg-warning text-dark">
                <h6 class="mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    Peringatan
                </h6>
            </div>
            <div class="card-body">
                <p class="small mb-0">
                    Customer ini memiliki rental aktif atau pending. 
                    Berhati-hati saat mengubah status atau menghapus customer.
                </p>
            </div>
        </div>
        @endif
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Set max date for birth_date to today
    const birthDateInput = document.getElementById('birth_date');
    const today = new Date().toISOString().split('T')[0];
    birthDateInput.max = today;
    
    // Format phone number
    const phoneInput = document.getElementById('phone');
    phoneInput.addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length > 0 && !value.startsWith('0')) {
            value = '0' + value;
        }
        e.target.value = value;
    });
    
    // Format ID card number
    const idCardInput = document.getElementById('id_card_number');
    idCardInput.addEventListener('input', function(e) {
        let value = e.target.value.replace(/\D/g, '');
        if (value.length > 16) {
            value = value.substring(0, 16);
        }
        e.target.value = value;
    });
});
</script>
@endsection
