<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Customer;
use App\Models\PsUnit;
use App\Models\Rental;
use Carbon\Carbon;

class RentalSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing rental data first
        Rental::truncate();

        // Get existing customers and PS units
        $customers = Customer::all();
        $psUnits = PsUnit::all();

        if ($customers->count() == 0 || $psUnits->count() == 0) {
            $this->command->error('Please create customers and PS units first!');
            return;
        }

        $this->command->info('Found ' . $customers->count() . ' customers and ' . $psUnits->count() . ' PS units.');

        // Get customer IDs safely
        $customerIds = $customers->pluck('id')->toArray();
        $psUnitIds = $psUnits->pluck('id')->toArray();

        // Ensure we have at least some data
        if (empty($customerIds) || empty($psUnitIds)) {
            $this->command->error('No valid customer or PS unit IDs found!');
            return;
        }

        // Create sample rentals
        $rentals = [
            [
                'rental_code' => 'RNT0001',
                'customer_id' => $customerIds[0],
                'ps_unit_id' => $psUnitIds[0],
                'start_datetime' => Carbon::now()->subDays(2),
                'end_datetime' => Carbon::now()->addDays(1),
                'rental_type' => 'daily',
                'duration' => 3,
                'price_per_unit' => 120000,
                'total_price' => 360000,
                'deposit' => 72000,
                'final_amount' => 360000,
                'status' => 'active',
                'payment_status' => 'paid',
                'notes' => 'Customer meminta 2 controller',
            ],
            [
                'rental_code' => 'RNT0002',
                'customer_id' => $customerIds[1] ?? $customerIds[0],
                'ps_unit_id' => $psUnitIds[1] ?? $psUnitIds[0],
                'start_datetime' => Carbon::now()->subDays(5),
                'end_datetime' => Carbon::now()->subDays(3),
                'actual_return_datetime' => Carbon::now()->subDays(3),
                'rental_type' => 'daily',
                'duration' => 2,
                'price_per_unit' => 100000,
                'total_price' => 200000,
                'deposit' => 40000,
                'final_amount' => 200000,
                'status' => 'completed',
                'payment_status' => 'paid',
                'notes' => 'Rental untuk acara ulang tahun',
            ],
            [
                'rental_code' => 'RNT0003',
                'customer_id' => $customerIds[2] ?? $customerIds[0],
                'ps_unit_id' => $psUnitIds[2] ?? $psUnitIds[0],
                'start_datetime' => Carbon::now()->subHours(8),
                'end_datetime' => Carbon::now()->subHours(2),
                'rental_type' => 'hourly',
                'duration' => 6,
                'price_per_unit' => 10000,
                'total_price' => 60000,
                'deposit' => 12000,
                'penalty' => 20000,
                'final_amount' => 80000,
                'status' => 'overdue',
                'payment_status' => 'partial',
                'notes' => 'Customer terlambat mengembalikan',
            ],
            [
                'rental_code' => 'RNT0004',
                'customer_id' => $customerIds[0],
                'ps_unit_id' => $psUnitIds[3] ?? $psUnitIds[0],
                'start_datetime' => Carbon::now()->addDays(1),
                'end_datetime' => Carbon::now()->addDays(3),
                'rental_type' => 'daily',
                'duration' => 2,
                'price_per_unit' => 85000,
                'total_price' => 170000,
                'deposit' => 34000,
                'final_amount' => 170000,
                'status' => 'pending',
                'payment_status' => 'unpaid',
                'notes' => 'Booking untuk weekend',
            ],
            [
                'rental_code' => 'RNT0005',
                'customer_id' => $customerIds[1] ?? $customerIds[0],
                'ps_unit_id' => $psUnitIds[1] ?? $psUnitIds[0],
                'start_datetime' => Carbon::now()->subDays(10),
                'end_datetime' => Carbon::now()->subDays(8),
                'actual_return_datetime' => Carbon::now()->subDays(8),
                'rental_type' => 'hourly',
                'duration' => 8,
                'price_per_unit' => 15000,
                'total_price' => 120000,
                'deposit' => 24000,
                'final_amount' => 120000,
                'status' => 'completed',
                'payment_status' => 'paid',
                'notes' => 'Rental untuk turnamen game',
            ],
        ];

        try {
            foreach ($rentals as $rentalData) {
                Rental::create($rentalData);
                $this->command->info('Created rental: ' . $rentalData['rental_code']);
            }

            $this->command->info('Sample rental data created successfully!');
            $this->command->info('Total Rentals: ' . Rental::count());

        } catch (\Exception $e) {
            $this->command->error('Error creating rental data: ' . $e->getMessage());
            $this->command->error('Stack trace: ' . $e->getTraceAsString());
        }
    }
}
