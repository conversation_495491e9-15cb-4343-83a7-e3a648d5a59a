@extends('layouts.app')

@section('title', 'Laporan Customer - Rental PS Admin')
@section('page-title', 'Laporan Customer')

@section('content')
<div class="row mb-4">
    <div class="col-md-6">
        <h4>
            <i class="fas fa-users me-2"></i>
            <PERSON><PERSON>an Customer
        </h4>
    </div>
    <div class="col-md-6 text-end">
        <a href="{{ route('admin.reports.index') }}" class="btn btn-secondary">
            <i class="fas fa-arrow-left me-1"></i>
            Kembali ke Dashboard
        </a>
    </div>
</div>

<!-- Statistik Customer -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="card text-center" style="background: white; border: 1px solid #e5e7eb;">
            <div class="card-body">
                <i class="fas fa-users fa-2x mb-2" style="color: #667eea;"></i>
                <h4 class="text-dark">{{ number_format($stats['total_customers']) }}</h4>
                <small class="text-muted">Total Customer</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card text-center" style="background: white; border: 1px solid #e5e7eb;">
            <div class="card-body">
                <i class="fas fa-user-check fa-2x mb-2" style="color: #11998e;"></i>
                <h4 class="text-dark">{{ number_format($stats['active_customers']) }}</h4>
                <small class="text-muted">Customer Aktif</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card text-center" style="background: white; border: 1px solid #e5e7eb;">
            <div class="card-body">
                <i class="fas fa-shopping-cart fa-2x mb-2" style="color: #ff9a9e;"></i>
                <h4 class="text-dark">{{ number_format($stats['customers_with_rentals']) }}</h4>
                <small class="text-muted">Pernah Rental</small>
            </div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="card text-center" style="background: white; border: 1px solid #e5e7eb;">
            <div class="card-body">
                <i class="fas fa-chart-line fa-2x mb-2" style="color: #ffa726;"></i>
                <h4 class="text-dark">{{ $stats['avg_rentals_per_customer'] }}</h4>
                <small class="text-muted">Rata-rata Rental</small>
            </div>
        </div>
    </div>
</div>

<!-- Filter -->
<div class="card mb-4">
    <div class="card-header">
        <h6 class="mb-0">
            <i class="fas fa-filter me-2"></i>
            Filter Customer
        </h6>
    </div>
    <div class="card-body">
        <form method="GET" action="{{ route('admin.reports.customers') }}">
            <div class="row">
                <div class="col-md-4 mb-3">
                    <label class="form-label">Status Customer</label>
                    <select name="status" class="form-select">
                        <option value="">Semua Status</option>
                        <option value="active" {{ request('status') == 'active' ? 'selected' : '' }}>Aktif</option>
                        <option value="inactive" {{ request('status') == 'inactive' ? 'selected' : '' }}>Nonaktif</option>
                        <option value="banned" {{ request('status') == 'banned' ? 'selected' : '' }}>Banned</option>
                    </select>
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Bergabung Dari</label>
                    <input type="date" name="start_date" class="form-control" value="{{ request('start_date') }}">
                </div>
                <div class="col-md-4 mb-3">
                    <label class="form-label">Bergabung Sampai</label>
                    <input type="date" name="end_date" class="form-control" value="{{ request('end_date') }}">
                </div>
            </div>
            <div class="row">
                <div class="col-md-12">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i>
                        Filter
                    </button>
                    <a href="{{ route('admin.reports.customers') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        Reset
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Tabel Customer -->
<div class="card">
    <div class="card-header">
        <h6 class="mb-0">
            <i class="fas fa-table me-2"></i>
            Detail Customer ({{ $customers->total() }} total)
        </h6>
    </div>
    <div class="card-body">
        @if($customers->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Nama Customer</th>
                            <th>Kontak</th>
                            <th>Bergabung</th>
                            <th>Total Rental</th>
                            <th>Total Pengeluaran</th>
                            <th>Status</th>
                            <th>Aksi</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($customers as $customer)
                        <tr>
                            <td>
                                <div>
                                    <strong>{{ $customer->name }}</strong>
                                    @if($customer->gender)
                                        @php
                                            $genderIcon = $customer->gender === 'male' ? 'mars' : 'venus';
                                            $genderClass = $customer->gender === 'male' ? 'text-primary' : 'text-danger';
                                        @endphp
                                        <i class="fas fa-{{ $genderIcon }} {{ $genderClass }} ms-1"></i>
                                    @endif
                                    @if($customer->birth_date)
                                        <br><small class="text-muted">{{ $customer->birth_date->age }} tahun</small>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div>
                                    <i class="fas fa-envelope me-1"></i>{{ $customer->email }}<br>
                                    @if($customer->phone)
                                        <i class="fas fa-phone me-1"></i>{{ $customer->phone }}
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ $customer->created_at->format('d/m/Y') }}</strong><br>
                                    <small class="text-muted">{{ $customer->created_at->diffForHumans() }}</small>
                                </div>
                            </td>
                            <td>
                                <div class="text-center">
                                    <span class="badge bg-info fs-6">{{ $customer->rentals_count }}</span>
                                    @if($customer->rentals_count > 0)
                                        <br><small class="text-muted">rental</small>
                                    @endif
                                </div>
                            </td>
                            <td>
                                <div class="text-end">
                                    @if($customer->total_spent > 0)
                                        <strong class="text-success">Rp {{ number_format($customer->total_spent, 0, ',', '.') }}</strong>
                                    @else
                                        <span class="text-muted">Rp 0</span>
                                    @endif
                                </div>
                            </td>
                            <td>
                                @php
                                    $statusClass = match($customer->status) {
                                        'active' => 'success',
                                        'inactive' => 'warning',
                                        'banned' => 'danger',
                                        default => 'secondary'
                                    };
                                    $statusText = match($customer->status) {
                                        'active' => 'Aktif',
                                        'inactive' => 'Nonaktif',
                                        'banned' => 'Banned',
                                        default => ucfirst($customer->status)
                                    };
                                @endphp
                                <span class="badge bg-{{ $statusClass }}">{{ $statusText }}</span>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.customers.show', $customer) }}" 
                                       class="btn btn-sm btn-outline-info" 
                                       title="Lihat Detail">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    @if($customer->rentals_count > 0)
                                        <a href="{{ route('admin.rentals.index', ['search' => $customer->name]) }}" 
                                           class="btn btn-sm btn-outline-primary" 
                                           title="Lihat Rental">
                                            <i class="fas fa-calendar-alt"></i>
                                        </a>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $customers->appends(request()->query())->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                <h5 class="text-muted">Tidak ada data customer</h5>
                <p class="text-muted">Tidak ada customer yang sesuai dengan filter yang dipilih.</p>
                <a href="{{ route('admin.reports.customers') }}" class="btn btn-primary">
                    <i class="fas fa-refresh me-1"></i>
                    Reset Filter
                </a>
            </div>
        @endif
    </div>
</div>

<!-- Customer Performance Analysis -->
@if($customers->count() > 0)
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-trophy me-2"></i>
                    Top 5 Customer Berdasarkan Pengeluaran
                </h6>
            </div>
            <div class="card-body">
                @php
                    $topSpenders = $customers->sortByDesc('total_spent')->take(5);
                @endphp
                @foreach($topSpenders as $index => $customer)
                    @if($customer->total_spent > 0)
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div class="d-flex align-items-center">
                                <span class="badge bg-primary me-2">{{ $index + 1 }}</span>
                                <div>
                                    <strong>{{ $customer->name }}</strong><br>
                                    <small class="text-muted">{{ $customer->rentals_count }} rental</small>
                                </div>
                            </div>
                            <div class="text-end">
                                <strong class="text-success">Rp {{ number_format($customer->total_spent, 0, ',', '.') }}</strong>
                            </div>
                        </div>
                        @if(!$loop->last)<hr>@endif
                    @endif
                @endforeach
            </div>
        </div>
    </div>

    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-chart-bar me-2"></i>
                    Top 5 Customer Berdasarkan Frekuensi
                </h6>
            </div>
            <div class="card-body">
                @php
                    $topFrequent = $customers->sortByDesc('rentals_count')->take(5);
                @endphp
                @foreach($topFrequent as $index => $customer)
                    @if($customer->rentals_count > 0)
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <div class="d-flex align-items-center">
                                <span class="badge bg-info me-2">{{ $index + 1 }}</span>
                                <div>
                                    <strong>{{ $customer->name }}</strong><br>
                                    <small class="text-muted">Bergabung {{ $customer->created_at->diffForHumans() }}</small>
                                </div>
                            </div>
                            <div class="text-end">
                                <strong class="text-primary">{{ $customer->rentals_count }} rental</strong>
                            </div>
                        </div>
                        @if(!$loop->last)<hr>@endif
                    @endif
                @endforeach
            </div>
        </div>
    </div>
</div>
@endif
@endsection
