<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>@yield('title', 'Rental PS Admin')</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <!-- Custom Admin Theme -->
    <link href="{{ asset('css/admin-theme.css') }}" rel="stylesheet">
    
    <style>
        html, body {
            height: 100%;
            margin: 0;
            padding: 0;
        }
        .sidebar {
            min-height: 100vh;
            height: 100vh;
            background: #60B5FF;
            position: fixed;
            top: 0;
            left: 0;
            width: 250px;
            z-index: 1000;
            overflow-y: auto;
        }
        .sidebar .nav-link {
            color: rgba(255,255,255,0.9);
            padding: 12px 20px;
            margin: 2px 0;
            border-radius: 8px;
            transition: all 0.3s;
            text-decoration: none;
        }
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: white;
            background: rgba(255,255,255,0.2);
        }

        /* Logout Button Styling */
        .sidebar .btn-outline-light {
            background: white !important;
            border: 1px solid white !important;
            color: #60B5FF !important;
            border-radius: 8px !important;
            transition: all 0.3s ease !important;
            font-weight: 500;
        }
        .sidebar .btn-outline-light:hover {
            background: #f8f9fa !important;
            border-color: #f8f9fa !important;
            color: #60B5FF !important;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        /* User Info Styling */
        .sidebar .text-white-50 {
            opacity: 0.8;
        }

        /* Sidebar Title Styling */
        .sidebar h4 {
            color: white !important;
            font-weight: 600;
        }
        .sidebar small {
            color: rgba(255,255,255,0.8) !important;
        }
        .main-content {
            background: #f8f9fa;
            min-height: 100vh;
        }
        .card {
            border: none;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .stats-card {
            background: #60B5FF;
            color: white;
        }
        .btn-primary {
            background: #60B5FF;
            border: none;
        }
        .btn-primary:hover {
            background: #1976D2;
        }
        .text-primary {
            color: #60B5FF !important;
        }
        .table td {
            color: #333 !important;
        }
        .table th {
            color: #333 !important;
        }
        .table .text-primary {
            color: #333 !important;
        }

        /* Table Header */
        .table thead th {
            background-color: #60B5FF !important;
            color: white !important;
        }

        /* Card Headers */
        .card-header.bg-primary,
        .card-header.bg-info,
        .card-header.bg-success {
            background-color: #60B5FF !important;
            color: white !important;
        }

        /* Badge Status Colors */
        .badge.bg-primary {
            background-color: #60B5FF !important;
            color: white !important;
        }
        .badge.bg-success {
            background-color: #28a745 !important;
            color: white !important;
        }
        .badge.bg-warning {
            background-color: #ffc107 !important;
            color: #212529 !important;
        }
        .badge.bg-danger {
            background-color: #dc3545 !important;
            color: white !important;
        }
        .badge.bg-secondary {
            background-color: #6c757d !important;
            color: white !important;
        }
        .badge.bg-info {
            background-color: #17a2b8 !important;
            color: white !important;
        }

        /* Button Text Colors */
        .btn {
            color: #333 !important;
        }
        .btn-primary {
            background-color: #60B5FF !important;
            color: white !important;
        }
        .btn-success {
            background-color: #60B5FF !important;
            color: white !important;
        }
        .btn-danger {
            color: white !important;
        }
        .btn-warning {
            color: #333 !important;
        }
        .btn-info {
            background-color: #60B5FF !important;
            color: white !important;
        }
        .btn-secondary {
            color: white !important;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3 col-lg-2 px-0">
                <div class="sidebar d-flex flex-column p-3">
                    <!-- Logo -->
                    <div class="text-center mb-4">
                        <h4 class="text-white">
                            <i class="fas fa-gamepad me-2"></i>
                            Rental PS
                        </h4>
                    </div>

                    <!-- Menu -->
                    <nav class="nav flex-column">
                        <a class="nav-link {{ request()->routeIs('admin.dashboard') ? 'active' : '' }}"
                           href="{{ route('admin.dashboard') }}">
                            <i class="fas fa-home me-2"></i>
                            Dashboard
                        </a>
                        <a class="nav-link {{ request()->routeIs('admin.rentals.*') ? 'active' : '' }}"
                           href="{{ route('admin.rentals.index') }}">
                            <i class="fas fa-calendar me-2"></i>
                            Rentals
                        </a>
                        <a class="nav-link {{ request()->routeIs('admin.ps-units.*') ? 'active' : '' }}"
                           href="{{ route('admin.ps-units.index') }}">
                            <i class="fas fa-gamepad me-2"></i>
                            PS Units
                        </a>
                        <a class="nav-link {{ request()->routeIs('admin.customers.*') ? 'active' : '' }}"
                           href="{{ route('admin.customers.index') }}">
                            <i class="fas fa-users me-2"></i>
                            Customers
                        </a>
                        <a class="nav-link {{ request()->routeIs('admin.reports.*') ? 'active' : '' }}"
                           href="{{ route('admin.reports.index') }}">
                            <i class="fas fa-chart-bar me-2"></i>
                            Reports
                        </a>
                    </nav>

                    <!-- User Info -->
                    <div class="mt-auto">
                        <form method="POST" action="{{ route('logout') }}">
                            @csrf
                            <button type="submit" class="btn btn-outline-light btn-sm w-100">
                                <i class="fas fa-sign-out-alt me-1"></i>
                                Logout
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- Main Content -->
            <div class="col-md-9 col-lg-10 px-0">
                <div class="main-content">
                    <!-- Top Navigation -->
                    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
                        <div class="container-fluid">
                            <span class="navbar-brand mb-0 h1 text-primary">@yield('page-title', 'Dashboard')</span>
                            <div class="navbar-nav ms-auto">
                                <span class="nav-item nav-link">
                                    <i class="fas fa-calendar me-1"></i>
                                    {{ now()->format('d M Y') }}
                                </span>
                            </div>
                        </div>
                    </nav>
                    
                    <!-- Page Content -->
                    <div class="container-fluid p-4">
                        @if(session('success'))
                            <div class="alert alert-success alert-dismissible fade show" role="alert">
                                <i class="fas fa-check-circle me-2"></i>
                                {{ session('success') }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        @endif
                        
                        @if(session('error'))
                            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                                <i class="fas fa-exclamation-circle me-2"></i>
                                {{ session('error') }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        @endif
                        
                        @yield('content')
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    @yield('scripts')
</body>
</html>
