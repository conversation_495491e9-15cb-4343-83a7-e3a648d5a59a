<?php

namespace App\Console\Commands;

use Illuminate\Console\Command;
use App\Http\Controllers\Admin\DashboardController;
use App\Models\PsUnit;
use App\Models\Customer;
use App\Models\Rental;

class TestDashboard extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:test-dashboard';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test dashboard controller logic';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $this->info('Testing Dashboard Controller Logic...');

        try {
            // Test dashboard statistics calculation
            $stats = [
                'total_ps_units' => PsUnit::count(),
                'available_units' => PsUnit::where('status', 'available')->count(),
                'rented_units' => PsUnit::where('status', 'rented')->count(),
                'maintenance_units' => PsUnit::where('status', 'maintenance')->count(),
                'total_customers' => Customer::count(),
                'active_rentals' => Rental::where('status', 'active')->count(),
                'pending_rentals' => Rental::where('status', 'pending')->count(),
                'overdue_rentals' => Rental::where('status', 'overdue')->count(),
                'today_revenue' => Rental::whereDate('created_at', today())
                                       ->where('payment_status', 'paid')
                                       ->sum('final_amount'),
                'month_revenue' => Rental::whereMonth('created_at', now()->month)
                                       ->whereYear('created_at', now()->year)
                                       ->where('payment_status', 'paid')
                                       ->sum('final_amount'),
            ];

            $this->info('Dashboard Statistics:');
            foreach ($stats as $key => $value) {
                $this->info("  {$key}: {$value}");
            }

            // Test recent rentals query
            $recentRentals = Rental::with(['customer', 'psUnit'])
                                  ->orderBy('created_at', 'desc')
                                  ->limit(10)
                                  ->get();

            $this->info("Recent Rentals Count: {$recentRentals->count()}");

            // Test alert units query
            $alertUnits = PsUnit::whereIn('status', ['maintenance', 'broken'])->get();
            $this->info("Alert Units Count: {$alertUnits->count()}");

            $this->info('Dashboard Controller logic is working correctly!');

        } catch (\Exception $e) {
            $this->error('Error testing dashboard: ' . $e->getMessage());
            $this->error('Stack trace: ' . $e->getTraceAsString());
        }
    }
}
