<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('rentals', function (Blueprint $table) {
            $table->id();
            $table->string('rental_code')->unique(); // Kode rental (RNT001, RNT002, dll)
            $table->foreignId('customer_id')->constrained()->onDelete('cascade'); // ID customer
            $table->foreignId('ps_unit_id')->constrained()->onDelete('cascade'); // ID PS unit
            $table->datetime('start_datetime'); // Tanggal dan waktu mulai rental
            $table->datetime('end_datetime'); // Tanggal dan waktu selesai rental
            $table->datetime('actual_return_datetime')->nullable(); // Waktu pengembalian aktual
            $table->enum('rental_type', ['hourly', 'daily']); // Tipe rental per jam atau per hari
            $table->integer('duration'); // Durasi rental (dalam jam atau hari)
            $table->decimal('price_per_unit', 10, 2); // Harga per unit (jam/hari)
            $table->decimal('total_price', 10, 2); // Total harga
            $table->decimal('deposit', 10, 2)->default(0); // Deposit
            $table->decimal('penalty', 10, 2)->default(0); // Denda keterlambatan
            $table->decimal('final_amount', 10, 2); // Jumlah akhir yang dibayar
            $table->enum('status', ['pending', 'active', 'completed', 'cancelled', 'overdue'])->default('pending');
            $table->enum('payment_status', ['unpaid', 'partial', 'paid', 'refunded'])->default('unpaid');
            $table->text('notes')->nullable(); // Catatan tambahan
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('rentals');
    }
};
