<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\PsUnit;
use Illuminate\Http\JsonResponse;

class PsUnitController extends Controller
{
    public function index(Request $request): JsonResponse
    {
        $query = PsUnit::query();

        // Filter by status
        if ($request->has('status')) {
            $query->where('status', $request->get('status'));
        }

        // Filter by PS type
        if ($request->has('ps_type')) {
            $query->where('ps_type', 'like', '%' . $request->get('ps_type') . '%');
        }

        // Search by unit code or description
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function($q) use ($search) {
                $q->where('unit_code', 'like', '%' . $search . '%')
                  ->orWhere('description', 'like', '%' . $search . '%')
                  ->orWhere('ps_type', 'like', '%' . $search . '%');
            });
        }

        // Sort by price
        if ($request->has('sort_by_price')) {
            $order = $request->get('sort_by_price') === 'desc' ? 'desc' : 'asc';
            $query->orderBy('price_per_hour', $order);
        } else {
            $query->orderBy('created_at', 'desc');
        }

        $psUnits = $query->paginate(10);

        return response()->json([
            'success' => true,
            'message' => 'PS Units retrieved successfully',
            'data' => $psUnits
        ]);
    }

    public function show(string $id): JsonResponse
    {
        $psUnit = PsUnit::find($id);

        if (!$psUnit) {
            return response()->json([
                'success' => false,
                'message' => 'PS Unit not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'message' => 'PS Unit retrieved successfully',
            'data' => $psUnit
        ]);
    }

    public function available(Request $request): JsonResponse
    {
        $query = PsUnit::available();

        // Filter by PS type
        if ($request->has('ps_type')) {
            $query->where('ps_type', 'like', '%' . $request->get('ps_type') . '%');
        }

        // Filter by price range
        if ($request->has('min_price')) {
            $query->where('price_per_hour', '>=', $request->get('min_price'));
        }

        if ($request->has('max_price')) {
            $query->where('price_per_hour', '<=', $request->get('max_price'));
        }

        // Sort by price
        if ($request->has('sort_by_price')) {
            $order = $request->get('sort_by_price') === 'desc' ? 'desc' : 'asc';
            $query->orderBy('price_per_hour', $order);
        } else {
            $query->orderBy('created_at', 'desc');
        }

        $psUnits = $query->paginate(10);

        return response()->json([
            'success' => true,
            'message' => 'Available PS Units retrieved successfully',
            'data' => $psUnits
        ]);
    }
}
