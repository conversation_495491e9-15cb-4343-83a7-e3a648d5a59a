<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\PsUnit;

class PsUnitSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $psUnits = [
            [
                'unit_code' => 'PS001',
                'ps_type' => 'PlayStation 4',
                'brand' => 'Sony',
                'description' => 'PlayStation 4 Slim 500GB dengan 2 controller',
                'price_per_hour' => 15000,
                'price_per_day' => 100000,
                'status' => 'available',
                'accessories' => ['2 Controller', 'HDMI Cable', 'Power Cable', '5 Game Disc'],
                'condition' => 'good',
                'purchase_date' => '2023-01-15',
            ],
            [
                'unit_code' => 'PS002',
                'ps_type' => 'PlayStation 4',
                'brand' => 'Sony',
                'description' => 'PlayStation 4 Pro 1TB dengan 2 controller',
                'price_per_hour' => 20000,
                'price_per_day' => 120000,
                'status' => 'available',
                'accessories' => ['2 Controller', 'HDMI Cable', 'Power Cable', '8 Game Disc'],
                'condition' => 'excellent',
                'purchase_date' => '2023-03-20',
            ],
            [
                'unit_code' => 'PS003',
                'ps_type' => 'PlayStation 5',
                'brand' => 'Sony',
                'description' => 'PlayStation 5 Standard Edition dengan 2 controller',
                'price_per_hour' => 25000,
                'price_per_day' => 150000,
                'status' => 'available',
                'accessories' => ['2 Controller', 'HDMI Cable', 'Power Cable', '3 Game Disc'],
                'condition' => 'excellent',
                'purchase_date' => '2023-06-10',
            ],
            [
                'unit_code' => 'PS004',
                'ps_type' => 'PlayStation 5',
                'brand' => 'Sony',
                'description' => 'PlayStation 5 Digital Edition dengan 2 controller',
                'price_per_hour' => 23000,
                'price_per_day' => 140000,
                'status' => 'rented',
                'accessories' => ['2 Controller', 'HDMI Cable', 'Power Cable'],
                'condition' => 'good',
                'purchase_date' => '2023-08-05',
            ],
            [
                'unit_code' => 'PS005',
                'ps_type' => 'PlayStation 4',
                'brand' => 'Sony',
                'description' => 'PlayStation 4 Slim 1TB dengan 2 controller',
                'price_per_hour' => 18000,
                'price_per_day' => 110000,
                'status' => 'maintenance',
                'accessories' => ['2 Controller', 'HDMI Cable', 'Power Cable', '6 Game Disc'],
                'condition' => 'fair',
                'purchase_date' => '2022-12-01',
            ],
        ];

        foreach ($psUnits as $unit) {
            PsUnit::create($unit);
        }
    }
}
