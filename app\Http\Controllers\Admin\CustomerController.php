<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Customer;
use Illuminate\Support\Facades\Hash;

class CustomerController extends Controller
{
    /**
     * Tampilkan daftar semua customer
     */
    public function index(Request $request)
    {
        $query = Customer::withCount('rentals');



        // Filter berdasarkan gender
        if ($request->filled('gender')) {
            $query->where('gender', $request->get('gender'));
        }

        // Search berdasarkan nama, email, atau phone
        if ($request->filled('search')) {
            $search = $request->get('search');
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%")
                  ->orWhere('phone', 'like', "%{$search}%");
            });
        }

        $customers = $query->orderBy('created_at', 'desc')->paginate(15);

        // Statistik untuk dashboard
        $stats = [
            'total' => Customer::count(),
            'male' => Customer::where('gender', 'male')->count(),
            'female' => Customer::where('gender', 'female')->count(),
        ];

        return view('admin.customers.index', compact('customers', 'stats'));
    }

    /**
     * Tampilkan form untuk membuat customer baru
     */
    public function create()
    {
        return view('admin.customers.create');
    }

    /**
     * Simpan customer baru ke database
     */
    public function store(Request $request)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:customers,email',
            'password' => 'required|string|min:6',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'birth_date' => 'nullable|date|before:today',
            'gender' => 'nullable|in:male,female',
            'id_card_number' => 'nullable|string|max:20|unique:customers,id_card_number',

        ]);

        Customer::create([
            'name' => $request->get('name'),
            'email' => $request->get('email'),
            'password' => Hash::make($request->get('password')),
            'phone' => $request->get('phone'),
            'address' => $request->get('address'),
            'birth_date' => $request->get('birth_date'),
            'gender' => $request->get('gender'),
            'id_card_number' => $request->get('id_card_number'),

        ]);

        return redirect()->route('admin.customers.index')
                        ->with('success', 'Customer berhasil ditambahkan!');
    }

    /**
     * Tampilkan detail customer
     */
    public function show(Customer $customer)
    {
        $customer->load(['rentals.psUnit']);
        
        // Statistik rental customer
        $rentalStats = [
            'total_rentals' => $customer->rentals->count(),
            'active_rentals' => $customer->rentals->where('status', 'active')->count(),
            'completed_rentals' => $customer->rentals->where('status', 'completed')->count(),
            'total_spent' => $customer->rentals->where('payment_status', 'paid')->sum('final_amount'),
        ];

        return view('admin.customers.show', compact('customer', 'rentalStats'));
    }

    /**
     * Tampilkan form edit customer
     */
    public function edit(Customer $customer)
    {
        return view('admin.customers.edit', compact('customer'));
    }

    /**
     * Update customer di database
     */
    public function update(Request $request, Customer $customer)
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|email|unique:customers,email,' . $customer->getAttribute('id'),
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string|max:500',
            'birth_date' => 'nullable|date|before:today',
            'gender' => 'nullable|in:male,female',
            'id_card_number' => 'nullable|string|max:20|unique:customers,id_card_number,' . $customer->getAttribute('id'),

        ]);

        $updateData = [
            'name' => $request->get('name'),
            'email' => $request->get('email'),
            'phone' => $request->get('phone'),
            'address' => $request->get('address'),
            'birth_date' => $request->get('birth_date'),
            'gender' => $request->get('gender'),
            'id_card_number' => $request->get('id_card_number'),

        ];

        // Update password jika diisi
        if ($request->filled('password')) {
            $request->validate([
                'password' => 'string|min:6',
            ]);
            $updateData['password'] = Hash::make($request->get('password'));
        }

        $customer->update($updateData);

        return redirect()->route('admin.customers.show', $customer)
                        ->with('success', 'Customer berhasil diupdate!');
    }

    /**
     * Hapus customer dari database
     */
    public function destroy(Customer $customer)
    {
        // Cek apakah customer memiliki rental aktif
        if ($customer->rentals()->whereIn('status', ['active', 'pending'])->exists()) {
            return back()->withErrors(['error' => 'Tidak dapat menghapus customer yang memiliki rental aktif atau pending.']);
        }

        $customer->delete();

        return redirect()->route('admin.customers.index')
                        ->with('success', 'Customer berhasil dihapus!');
    }


}
