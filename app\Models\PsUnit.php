<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Builder;
use App\Helpers\PsImageHelper;

class PsUnit extends Model
{
    protected $fillable = [
        'unit_code',
        'ps_type',
        'brand',
        'description',
        'price_per_hour',
        'price_per_day',
        'status',
        'accessories',
        'condition',
        'purchase_date',
        'total_stock',
        'available_stock',
    ];

    protected $casts = [
        'accessories' => 'array',
        'price_per_hour' => 'decimal:2',
        'price_per_day' => 'decimal:2',
        'purchase_date' => 'date',
    ];

    // Relationship dengan rentals
    public function rentals(): HasMany
    {
        return $this->hasMany(Rental::class);
    }

    // Scope untuk unit yang tersedia
    public function scopeAvailable(Builder $query): Builder
    {
        return $query->where('status', 'available');
    }

    // Scope untuk unit yang sedang disewa
    public function scopeRented(Builder $query): Builder
    {
        return $query->where('status', 'rented');
    }

    // Check apakah unit sedang disewa
    public function isRented(): bool
    {
        return $this->getAttribute('status') === 'rented';
    }

    // Check apakah unit tersedia
    public function isAvailable(): bool
    {
        return $this->getAttribute('status') === 'available' && $this->getAttribute('available_stock') > 0;
    }

    // Check apakah stok tersedia
    public function hasStock(): bool
    {
        return $this->getAttribute('available_stock') > 0;
    }

    // Kurangi stok saat rental
    public function decreaseStock(int $quantity = 1): bool
    {
        if ($this->getAttribute('available_stock') >= $quantity) {
            $this->available_stock = $this->getAttribute('available_stock') - $quantity;

            // Update status jika stok habis
            if ($this->getAttribute('available_stock') == 0) {
                $this->status = 'rented';
            }

            return $this->save();
        }

        return false;
    }

    // Tambah stok saat rental selesai
    public function increaseStock(int $quantity = 1): bool
    {
        // Pastikan available stock tidak melebihi total stock
        $newAvailableStock = $this->getAttribute('available_stock') + $quantity;

        if ($newAvailableStock > $this->getAttribute('total_stock')) {
            $this->available_stock = $this->getAttribute('total_stock');
        } else {
            $this->available_stock = $newAvailableStock;
        }

        // Update status jika ada stok
        if ($this->getAttribute('available_stock') > 0 && $this->getAttribute('status') === 'rented') {
            $this->status = 'available';
        }

        return $this->save();
    }

    // Get stock status text
    public function getStockStatusAttribute(): string
    {
        if ($this->getAttribute('available_stock') == 0) {
            return 'Habis';
        } elseif ($this->getAttribute('available_stock') < $this->getAttribute('total_stock') * 0.3) {
            return 'Sedikit';
        } else {
            return 'Tersedia';
        }
    }

    // Get PlayStation image
    public function getPsImageAttribute(): string
    {
        // Try to get custom_image, handle if column doesn't exist
        $customImage = null;
        try {
            $customImage = $this->getAttribute('custom_image');
        } catch (\Exception $e) {
            // Column doesn't exist yet, use null
            $customImage = null;
        }

        return PsImageHelper::getPsImage($this->ps_type, $customImage);
    }

    // Get PlayStation display name
    public function getPsDisplayNameAttribute(): string
    {
        return PsImageHelper::getPsDisplayName($this->ps_type);
    }

    // Check if PS image exists
    public function hasPsImage(): bool
    {
        return PsImageHelper::imageExists($this->ps_type);
    }
}
