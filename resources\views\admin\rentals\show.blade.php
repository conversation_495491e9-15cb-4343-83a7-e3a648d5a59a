@extends('layouts.app')

@section('title', 'Detail Rental - Rental PS Admin')
@section('page-title', 'Detail Rental')

@section('content')
<div class="row">
    <div class="col-md-8">
        <!-- Rental Information -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    Informasi Rental
                </h5>
                <div>
                    @php
                        $statusClass = match($rental->status) {
                            'pending' => 'warning',
                            'active' => 'primary',
                            'completed' => 'success',
                            'cancelled' => 'secondary',
                            'overdue' => 'danger',
                            default => 'secondary'
                        };
                        $statusText = match($rental->status) {
                            'pending' => 'Pending',
                            'active' => 'Aktif',
                            'completed' => 'Selesai',
                            'cancelled' => 'Dibatalkan',
                            'overdue' => 'Terlambat',
                            default => ucfirst($rental->status)
                        };
                    @endphp
                    <span class="badge bg-{{ $statusClass }} fs-6">{{ $statusText }}</span>
                </div>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold">Kode Rental:</td>
                                <td>{{ $rental->rental_code }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Customer:</td>
                                <td>
                                    <strong>{{ $rental->customer->name }}</strong><br>
                                    <small class="text-muted">{{ $rental->customer->phone }}</small><br>
                                    <small class="text-muted">{{ $rental->customer->email }}</small>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">PS Unit:</td>
                                <td>
                                    <span class="badge bg-secondary">{{ $rental->psUnit->unit_code }}</span><br>
                                    {{ $rental->psUnit->ps_type }} - {{ $rental->psUnit->brand }}
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Tipe Rental:</td>
                                <td>{{ $rental->rental_type === 'hourly' ? 'Per Jam' : 'Per Hari' }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Durasi:</td>
                                <td>{{ $rental->duration }} {{ $rental->rental_type === 'hourly' ? 'jam' : 'hari' }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td class="fw-bold">Waktu Mulai:</td>
                                <td>{{ $rental->start_datetime->format('d/m/Y H:i') }}</td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Waktu Selesai:</td>
                                <td>{{ $rental->end_datetime->format('d/m/Y H:i') }}</td>
                            </tr>
                            @if($rental->actual_return_datetime)
                            <tr>
                                <td class="fw-bold">Waktu Kembali:</td>
                                <td>
                                    {{ $rental->actual_return_datetime->format('d/m/Y H:i') }}
                                    @if($rental->actual_return_datetime > $rental->end_datetime)
                                        <br><small class="text-danger">Terlambat {{ $rental->actual_return_datetime->diffForHumans($rental->end_datetime, true) }}</small>
                                    @endif
                                </td>
                            </tr>
                            @endif
                            <tr>
                                <td class="fw-bold">Status Pembayaran:</td>
                                <td>
                                    @php
                                        $paymentClass = match($rental->payment_status) {
                                            'unpaid' => 'danger',
                                            'partial' => 'warning',
                                            'paid' => 'success',
                                            'refunded' => 'info',
                                            default => 'secondary'
                                        };
                                        $paymentText = match($rental->payment_status) {
                                            'unpaid' => 'Belum Bayar',
                                            'partial' => 'Sebagian',
                                            'paid' => 'Lunas',
                                            'refunded' => 'Dikembalikan',
                                            default => ucfirst($rental->payment_status)
                                        };
                                    @endphp
                                    <span class="badge bg-{{ $paymentClass }}">{{ $paymentText }}</span>
                                </td>
                            </tr>
                            <tr>
                                <td class="fw-bold">Tanggal Dibuat:</td>
                                <td>{{ $rental->created_at->format('d/m/Y H:i') }}</td>
                            </tr>
                        </table>
                    </div>
                </div>

                @if($rental->notes)
                <div class="mt-3">
                    <h6>Catatan:</h6>
                    <div class="alert alert-info">
                        {{ $rental->notes }}
                    </div>
                </div>
                @endif
            </div>
        </div>

        <!-- Price Details -->
        <div class="card mt-4">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-money-bill-wave me-2"></i>
                    Detail Harga
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <table class="table table-borderless">
                            <tr>
                                <td>Harga per {{ $rental->rental_type === 'hourly' ? 'jam' : 'hari' }}:</td>
                                <td class="text-end">Rp {{ number_format($rental->price_per_unit, 0, ',', '.') }}</td>
                            </tr>
                            <tr>
                                <td>Durasi:</td>
                                <td class="text-end">{{ $rental->duration }} {{ $rental->rental_type === 'hourly' ? 'jam' : 'hari' }}</td>
                            </tr>
                            <tr>
                                <td>Total Harga:</td>
                                <td class="text-end">Rp {{ number_format($rental->total_price, 0, ',', '.') }}</td>
                            </tr>
                            <tr>
                                <td>Deposit:</td>
                                <td class="text-end">Rp {{ number_format($rental->deposit, 0, ',', '.') }}</td>
                            </tr>
                            @if($rental->penalty > 0)
                            <tr class="text-danger">
                                <td>Denda Keterlambatan:</td>
                                <td class="text-end">Rp {{ number_format($rental->penalty, 0, ',', '.') }}</td>
                            </tr>
                            @endif
                        </table>
                    </div>
                    <div class="col-md-6">
                        <div class="alert alert-success text-center">
                            <h4 class="mb-0">Total Akhir</h4>
                            <h2 class="text-success">Rp {{ number_format($rental->final_amount, 0, ',', '.') }}</h2>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <!-- Quick Actions -->
        <div class="card">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    Aksi Cepat
                </h6>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="{{ route('admin.rentals.edit', $rental) }}" class="btn btn-warning">
                        <i class="fas fa-edit me-1"></i>
                        Edit Rental
                    </a>

                    @if($rental->status === 'active')
                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#returnModal">
                            <i class="fas fa-undo me-1"></i>
                            Kembalikan PS Unit
                        </button>

                        <button type="button" class="btn btn-info" data-bs-toggle="modal" data-bs-target="#extendModal">
                            <i class="fas fa-clock me-1"></i>
                            Perpanjang Rental
                        </button>
                    @endif

                    @if($rental->status !== 'active')
                        <form method="POST" action="{{ route('admin.rentals.destroy', $rental) }}" 
                              onsubmit="return confirm('Yakin ingin menghapus rental ini?')">
                            @csrf
                            @method('DELETE')
                            <button type="submit" class="btn btn-danger">
                                <i class="fas fa-trash me-1"></i>
                                Hapus Rental
                            </button>
                        </form>
                    @endif

                    <a href="{{ route('admin.rentals.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Kembali ke Daftar
                    </a>
                </div>
            </div>
        </div>

        <!-- PS Unit Info -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0">
                    <i class="fas fa-gamepad me-2"></i>
                    Info PS Unit
                </h6>
            </div>
            <div class="card-body">
                <div class="text-center mb-3">
                    <h5>{{ $rental->psUnit->unit_code }}</h5>
                    <p class="text-muted">{{ $rental->psUnit->ps_type }} - {{ $rental->psUnit->brand }}</p>
                </div>
                
                <table class="table table-sm table-borderless">
                    <tr>
                        <td>Status:</td>
                        <td>
                            @php
                                $unitStatusClass = match($rental->psUnit->status) {
                                    'available' => 'success',
                                    'rented' => 'primary',
                                    'maintenance' => 'warning',
                                    'broken' => 'danger',
                                    default => 'secondary'
                                };
                                $unitStatusText = match($rental->psUnit->status) {
                                    'available' => 'Tersedia',
                                    'rented' => 'Disewa',
                                    'maintenance' => 'Maintenance',
                                    'broken' => 'Rusak',
                                    default => ucfirst($rental->psUnit->status)
                                };
                            @endphp
                            <span class="badge bg-{{ $unitStatusClass }}">{{ $unitStatusText }}</span>
                        </td>
                    </tr>
                    <tr>
                        <td>Kondisi:</td>
                        <td>{{ ucfirst($rental->psUnit->condition) }}</td>
                    </tr>
                    <tr>
                        <td>Harga/Jam:</td>
                        <td>Rp {{ number_format($rental->psUnit->price_per_hour, 0, ',', '.') }}</td>
                    </tr>
                    <tr>
                        <td>Harga/Hari:</td>
                        <td>Rp {{ number_format($rental->psUnit->price_per_day, 0, ',', '.') }}</td>
                    </tr>
                </table>

                @if($rental->psUnit->accessories)
                <div class="mt-3">
                    <h6>Aksesoris:</h6>
                    <ul class="list-unstyled">
                        @foreach($rental->psUnit->accessories as $accessory)
                            <li><i class="fas fa-check text-success me-1"></i> {{ $accessory }}</li>
                        @endforeach
                    </ul>
                </div>
                @endif
            </div>
        </div>
    </div>
</div>

@if($rental->status === 'active')
<!-- Return Modal -->
<div class="modal fade" id="returnModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Kembalikan PS Unit</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ route('admin.rentals.return', $rental) }}">
                @csrf
                @method('PATCH')
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        PS Unit akan dikembalikan pada waktu sekarang. Jika terlambat, denda akan dihitung otomatis.
                    </div>
                    
                    <div class="mb-3">
                        <strong>Waktu Seharusnya Kembali:</strong> {{ $rental->end_datetime->format('d/m/Y H:i') }}<br>
                        <strong>Waktu Sekarang:</strong> {{ now()->format('d/m/Y H:i') }}
                    </div>

                    @if(now() > $rental->end_datetime)
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Terlambat!</strong> Denda akan dikenakan sebesar 10% dari harga per unit untuk setiap jam keterlambatan.
                        </div>
                    @endif
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-success">Kembalikan PS Unit</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Extend Modal -->
<div class="modal fade" id="extendModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Perpanjang Rental</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST" action="{{ route('admin.rentals.extend', $rental) }}">
                @csrf
                @method('PATCH')
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="extend_duration" class="form-label">
                            Perpanjang Durasi ({{ $rental->rental_type === 'hourly' ? 'jam' : 'hari' }})
                        </label>
                        <input type="number" name="extend_duration" id="extend_duration" 
                               class="form-control" min="1" required>
                    </div>
                    
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        Biaya tambahan: Rp {{ number_format($rental->price_per_unit, 0, ',', '.') }} 
                        per {{ $rental->rental_type === 'hourly' ? 'jam' : 'hari' }}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="submit" class="btn btn-info">Perpanjang Rental</button>
                </div>
            </form>
        </div>
    </div>
</div>
@endif
@endsection
