<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ps_units', function (Blueprint $table) {
            $table->integer('total_stock')->default(1)->after('condition');
            $table->integer('available_stock')->default(1)->after('total_stock');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ps_units', function (Blueprint $table) {
            $table->dropColumn(['total_stock', 'available_stock']);
        });
    }
};
