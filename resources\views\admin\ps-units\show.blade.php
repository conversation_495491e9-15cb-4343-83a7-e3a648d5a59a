@extends('layouts.app')

@section('title', 'PS Unit Details - Rental PS Admin')
@section('page-title', 'PS Unit Details')

@section('content')
<div class="row">
    <div class="col-md-8 mx-auto">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0">
                    <i class="fas fa-gamepad me-2"></i>
                    PS Unit Details
                </h5>
                <div>
                    @php
                        $statusClass = match($psUnit->status) {
                            'available' => 'success',
                            'rented' => 'primary',
                            'maintenance' => 'warning',
                            'broken' => 'danger',
                            default => 'secondary'
                        };
                    @endphp
                    <span class="badge bg-{{ $statusClass }}">{{ ucfirst($psUnit->status) }}</span>
                </div>
            </div>
            <div class="card-body">
                <!-- PS Image Section -->
                <div class="row mb-4">
                    <div class="col-md-4 text-center">
                        <div class="ps-image-container">
                            <img src="{{ $psUnit->ps_image }}"
                                 alt="{{ $psUnit->ps_display_name }}"
                                 class="img-fluid rounded shadow-sm"
                                 style="max-height: 200px; object-fit: contain;"
                                 onerror="this.src='{{ asset('images/ps-units/placeholder.svg') }}'">
                        </div>
                        <h6 class="mt-2 text-muted">{{ $psUnit->ps_display_name }}</h6>
                        @php
                            $hasCustomImage = false;
                            try {
                                $hasCustomImage = !empty($psUnit->getAttribute('custom_image'));
                            } catch (\Exception $e) {
                                $hasCustomImage = false;
                            }
                        @endphp
                        @if($hasCustomImage)
                            <small class="text-success">
                                <i class="fas fa-check-circle"></i> Custom Image
                            </small>
                        @endif
                    </div>
                    <div class="col-md-8">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">Unit Code</label>
                                <p class="form-control-plaintext">{{ $psUnit->unit_code }}</p>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label class="form-label fw-bold">PS Type</label>
                                <p class="form-control-plaintext">{{ $psUnit->ps_type }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">Brand</label>
                        <p class="form-control-plaintext">{{ $psUnit->brand }}</p>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">Condition</label>
                        <p class="form-control-plaintext">
                            @php
                                $conditionClass = match($psUnit->condition) {
                                    'excellent' => 'success',
                                    'good' => 'info',
                                    'fair' => 'warning',
                                    'poor' => 'danger',
                                    default => 'secondary'
                                };
                            @endphp
                            <span class="badge bg-{{ $conditionClass }}">{{ ucfirst($psUnit->condition) }}</span>
                        </p>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">Price per Hour</label>
                        <p class="form-control-plaintext">Rp {{ number_format($psUnit->price_per_hour, 0, ',', '.') }}</p>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">Price per Day</label>
                        <p class="form-control-plaintext">Rp {{ number_format($psUnit->price_per_day, 0, ',', '.') }}</p>
                    </div>
                </div>
                
                @if($psUnit->purchase_date)
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">Purchase Date</label>
                        <p class="form-control-plaintext">{{ $psUnit->purchase_date->format('d M Y') }}</p>
                    </div>
                </div>
                @endif
                
                @if($psUnit->accessories)
                <div class="mb-3">
                    <label class="form-label fw-bold">Accessories</label>
                    <p class="form-control-plaintext">{{ is_array($psUnit->accessories) ? implode(', ', $psUnit->accessories) : $psUnit->accessories }}</p>
                </div>
                @endif
                
                @if($psUnit->description)
                <div class="mb-3">
                    <label class="form-label fw-bold">Description</label>
                    <p class="form-control-plaintext">{{ $psUnit->description }}</p>
                </div>
                @endif
                
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">Created At</label>
                        <p class="form-control-plaintext">{{ $psUnit->created_at->format('d M Y H:i') }}</p>
                    </div>
                    
                    <div class="col-md-6 mb-3">
                        <label class="form-label fw-bold">Last Updated</label>
                        <p class="form-control-plaintext">{{ $psUnit->updated_at->format('d M Y H:i') }}</p>
                    </div>
                </div>
                
                <div class="d-flex justify-content-between">
                    <a href="{{ route('admin.ps-units.index') }}" class="btn btn-secondary">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back to List
                    </a>
                    <div>
                        <a href="{{ route('admin.ps-units.edit', $psUnit) }}" class="btn btn-warning">
                            <i class="fas fa-edit me-1"></i>
                            Edit
                        </a>
                        @if($psUnit->status !== 'rented')
                            <form method="POST" 
                                  action="{{ route('admin.ps-units.destroy', $psUnit) }}" 
                                  class="d-inline"
                                  onsubmit="return confirm('Are you sure you want to delete this PS Unit?')">
                                @csrf
                                @method('DELETE')
                                <button type="submit" class="btn btn-danger">
                                    <i class="fas fa-trash me-1"></i>
                                    Delete
                                </button>
                            </form>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
@endsection
