@extends('layouts.app')

@section('title', 'PS Units - Rental PS Admin')
@section('page-title', 'PS Units Management')

@section('content')
<div class="row mb-4">
    <div class="col-md-6">
        <h4>
            <i class="fas fa-gamepad me-2"></i>
            PS Units
        </h4>
    </div>
    <div class="col-md-6 text-end">
        <a href="{{ route('admin.ps-units.create') }}" class="btn btn-primary">
            <i class="fas fa-plus me-1"></i>
            Add New PS Unit
        </a>
    </div>
</div>

<!-- Filter Form -->
<div class="card mb-4">
    <div class="card-header">
        <h6 class="mb-0">
            <i class="fas fa-filter me-2"></i>
            Filter & Pencarian
        </h6>
    </div>
    <div class="card-body">
        <form method="GET" action="{{ route('admin.ps-units.index') }}">
            <div class="row">
                <div class="col-md-3 mb-3">
                    <label class="form-label">Status Unit</label>
                    <select name="status" class="form-select">
                        <option value="">Semua Status</option>
                        <option value="available" {{ request()->get('status') == 'available' ? 'selected' : '' }}>Available</option>
                        <option value="rented" {{ request()->get('status') == 'rented' ? 'selected' : '' }}>Rented</option>
                        <option value="maintenance" {{ request()->get('status') == 'maintenance' ? 'selected' : '' }}>Maintenance</option>
                        <option value="broken" {{ request()->get('status') == 'broken' ? 'selected' : '' }}>Broken</option>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label class="form-label">Kondisi Unit</label>
                    <select name="condition" class="form-select">
                        <option value="">Semua Kondisi</option>
                        <option value="excellent" {{ request()->get('condition') == 'excellent' ? 'selected' : '' }}>Excellent</option>
                        <option value="good" {{ request()->get('condition') == 'good' ? 'selected' : '' }}>Good</option>
                        <option value="fair" {{ request()->get('condition') == 'fair' ? 'selected' : '' }}>Fair</option>
                        <option value="poor" {{ request()->get('condition') == 'poor' ? 'selected' : '' }}>Poor</option>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label class="form-label">Tipe PS</label>
                    <select name="ps_type" class="form-select">
                        <option value="">Semua Tipe</option>
                        <option value="PS4" {{ request()->get('ps_type') == 'PS4' ? 'selected' : '' }}>PS4</option>
                        <option value="PS5" {{ request()->get('ps_type') == 'PS5' ? 'selected' : '' }}>PS5</option>
                        <option value="Xbox One" {{ request()->get('ps_type') == 'Xbox One' ? 'selected' : '' }}>Xbox One</option>
                        <option value="Xbox Series X" {{ request()->get('ps_type') == 'Xbox Series X' ? 'selected' : '' }}>Xbox Series X</option>
                    </select>
                </div>
                <div class="col-md-3 mb-3">
                    <label class="form-label">Brand</label>
                    <select name="brand" class="form-select">
                        <option value="">Semua Brand</option>
                        <option value="Sony" {{ request()->get('brand') == 'Sony' ? 'selected' : '' }}>Sony</option>
                        <option value="Microsoft" {{ request()->get('brand') == 'Microsoft' ? 'selected' : '' }}>Microsoft</option>
                    </select>
                </div>
            </div>
            <div class="row">
                <div class="col-md-6 mb-3">
                    <label class="form-label">Pencarian</label>
                    <input type="text" name="search" class="form-control"
                           placeholder="Cari berdasarkan kode unit atau tipe PS..."
                           value="{{ request()->get('search') }}">
                </div>
                <div class="col-md-6 mb-3 d-flex align-items-end">
                    <button type="submit" class="btn btn-primary me-2">
                        <i class="fas fa-search me-1"></i>
                        Cari
                    </button>
                    <a href="{{ route('admin.ps-units.index') }}" class="btn btn-outline-secondary">
                        <i class="fas fa-times me-1"></i>
                        Reset
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<div class="card">
    <div class="card-body">
        @if($psUnits->count() > 0)
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead>
                        <tr>
                            <th>Image</th>
                            <th>Unit Code</th>
                            <th>PS Type</th>
                            <th>Brand</th>
                            <th>Stock</th>
                            <th>Price/Hour</th>
                            <th>Price/Day</th>
                            <th>Status</th>
                            <th>Condition</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach($psUnits as $unit)
                        <tr>
                            <td>
                                <div class="text-center">
                                    <img src="{{ $unit->ps_image }}"
                                         alt="{{ $unit->ps_display_name }}"
                                         class="img-thumbnail"
                                         style="width: 60px; height: 60px; object-fit: contain;"
                                         onerror="this.src='{{ asset('images/ps-units/placeholder.svg') }}'">
                                </div>
                            </td>
                            <td>
                                <strong>{{ $unit->unit_code }}</strong>
                            </td>
                            <td>
                                <div>
                                    <strong>{{ $unit->ps_display_name }}</strong>
                                    <br><small class="text-muted">{{ $unit->ps_type }}</small>
                                </div>
                            </td>
                            <td>{{ $unit->brand }}</td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <span class="badge bg-light text-dark me-2">
                                        {{ $unit->available_stock ?? 1 }}/{{ $unit->total_stock ?? 1 }}
                                    </span>
                                    @php
                                        $stockStatus = '';
                                        $stockClass = '';
                                        $availableStock = $unit->available_stock ?? 1;
                                        $totalStock = $unit->total_stock ?? 1;

                                        if ($availableStock == 0) {
                                            $stockStatus = 'Habis';
                                            $stockClass = 'danger';
                                        } elseif ($availableStock < $totalStock * 0.3) {
                                            $stockStatus = 'Sedikit';
                                            $stockClass = 'warning';
                                        } else {
                                            $stockStatus = 'Tersedia';
                                            $stockClass = 'success';
                                        }
                                    @endphp
                                    <small class="text-{{ $stockClass }}">{{ $stockStatus }}</small>
                                </div>
                            </td>
                            <td>Rp {{ number_format($unit->price_per_hour, 0, ',', '.') }}</td>
                            <td>Rp {{ number_format($unit->price_per_day, 0, ',', '.') }}</td>
                            <td>
                                @php
                                    $statusClass = match($unit->status) {
                                        'available' => 'success',
                                        'rented' => 'primary',
                                        'maintenance' => 'warning',
                                        'broken' => 'danger',
                                        default => 'secondary'
                                    };
                                @endphp
                                <span class="badge bg-{{ $statusClass }}">{{ ucfirst($unit->status) }}</span>
                            </td>
                            <td>
                                @php
                                    $conditionClass = match($unit->condition) {
                                        'excellent' => 'success',
                                        'good' => 'info',
                                        'fair' => 'warning',
                                        'poor' => 'danger',
                                        default => 'secondary'
                                    };
                                @endphp
                                <span class="badge bg-{{ $conditionClass }}">{{ ucfirst($unit->condition) }}</span>
                            </td>
                            <td class="text-center">
                                <div class="btn-group" role="group">
                                    <a href="{{ route('admin.ps-units.show', $unit) }}"
                                       class="btn btn-outline-secondary"
                                       title="View Details">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ route('admin.ps-units.edit', $unit) }}"
                                       class="btn btn-outline-warning"
                                       title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    @if($unit->status !== 'rented')
                                        <form method="POST"
                                              action="{{ route('admin.ps-units.destroy', $unit) }}"
                                              style="display: contents;"
                                              onsubmit="return confirm('Are you sure you want to delete this PS Unit?')">
                                            @csrf
                                            @method('DELETE')
                                            <button type="submit"
                                                    class="btn btn-outline-danger"
                                                    title="Delete">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                        </form>
                                    @else
                                        <button class="btn btn-outline-danger"
                                                title="Cannot delete - Unit is rented"
                                                disabled>
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    @endif
                                </div>
                            </td>
                        </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
            
            <!-- Pagination -->
            <div class="d-flex justify-content-center mt-4">
                {{ $psUnits->links() }}
            </div>
        @else
            <div class="text-center py-5">
                <i class="fas fa-gamepad fa-4x text-muted mb-3"></i>
                <h5 class="text-muted">No PS Units Found</h5>
                <p class="text-muted">Start by adding your first PS unit to the inventory.</p>
                <a href="{{ route('admin.ps-units.create') }}" class="btn btn-primary">
                    <i class="fas fa-plus me-1"></i>
                    Add First PS Unit
                </a>
            </div>
        @endif
    </div>
</div>

<!-- Quick Stats -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-check-circle fa-2x mb-2 text-success"></i>
                <h4>{{ $psUnits->where('status', 'available')->count() }}</h4>
                <small class="text-muted">Available</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-clock fa-2x mb-2 text-primary"></i>
                <h4>{{ $psUnits->where('status', 'rented')->count() }}</h4>
                <small class="text-muted">Rented</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-tools fa-2x mb-2 text-warning"></i>
                <h4>{{ $psUnits->where('status', 'maintenance')->count() }}</h4>
                <small class="text-muted">Maintenance</small>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <i class="fas fa-exclamation-triangle fa-2x mb-2 text-danger"></i>
                <h4>{{ $psUnits->where('status', 'broken')->count() }}</h4>
                <small class="text-muted">Broken</small>
            </div>
        </div>
    </div>
</div>
@endsection
