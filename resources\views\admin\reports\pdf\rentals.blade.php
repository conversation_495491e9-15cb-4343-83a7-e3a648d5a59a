<!DOCTYPE html>
<html lang="id">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Laporan Detail Rental - {{ $period ?? 'Semua Periode' }}</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            font-size: 11px;
            line-height: 1.4;
            color: #333;
            margin: 0;
            padding: 15px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 25px;
            border-bottom: 2px solid #007bff;
            padding-bottom: 15px;
        }
        
        .header h1 {
            color: #007bff;
            margin: 0;
            font-size: 22px;
        }
        
        .header h2 {
            color: #666;
            margin: 5px 0;
            font-size: 14px;
            font-weight: normal;
        }
        
        .filters {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-size: 10px;
        }
        
        .summary {
            display: table;
            width: 100%;
            margin-bottom: 25px;
        }
        
        .summary-item {
            display: table-cell;
            width: 25%;
            text-align: center;
            padding: 12px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
        }
        
        .summary-item:first-child {
            border-radius: 5px 0 0 5px;
        }
        
        .summary-item:last-child {
            border-radius: 0 5px 5px 0;
        }
        
        .summary-value {
            font-size: 16px;
            font-weight: bold;
            color: #007bff;
            display: block;
        }
        
        .summary-label {
            font-size: 9px;
            color: #666;
            margin-top: 3px;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 20px;
        }
        
        th, td {
            border: 1px solid #dee2e6;
            padding: 6px;
            text-align: left;
        }
        
        th {
            background: #007bff;
            color: white;
            font-weight: bold;
            font-size: 10px;
        }
        
        td {
            font-size: 9px;
        }
        
        .text-center {
            text-align: center;
        }
        
        .text-right {
            text-align: right;
        }
        
        .status-badge {
            padding: 2px 5px;
            border-radius: 3px;
            font-size: 8px;
            font-weight: bold;
            text-transform: uppercase;
        }
        
        .status-pending {
            background: #fff3cd;
            color: #856404;
        }
        
        .status-active {
            background: #d4edda;
            color: #155724;
        }
        
        .status-completed {
            background: #cce5ff;
            color: #004085;
        }
        
        .status-cancelled {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status-overdue {
            background: #f5c6cb;
            color: #721c24;
        }
        
        .payment-paid {
            background: #d4edda;
            color: #155724;
        }
        
        .payment-unpaid {
            background: #f8d7da;
            color: #721c24;
        }
        
        .payment-partial {
            background: #fff3cd;
            color: #856404;
        }
        
        .footer {
            margin-top: 25px;
            text-align: center;
            font-size: 9px;
            color: #666;
            border-top: 1px solid #dee2e6;
            padding-top: 10px;
        }
        
        .no-data {
            text-align: center;
            padding: 30px;
            color: #666;
            font-style: italic;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <h1>RENTAL PLAYSTATION</h1>
        <h2>Laporan Detail Rental</h2>
    </div>
    
    <!-- Filters Info -->
    @if(isset($filters) && count($filters) > 0)
    <div class="filters">
        <strong>Filter yang diterapkan:</strong>
        @foreach($filters as $key => $value)
            {{ ucfirst(str_replace('_', ' ', $key)) }}: {{ $value }}{{ !$loop->last ? ' | ' : '' }}
        @endforeach
    </div>
    @endif
    
    <!-- Summary -->
    <div class="summary">
        <div class="summary-item">
            <span class="summary-value">{{ $stats['total_rentals'] ?? 0 }}</span>
            <div class="summary-label">Total Rental</div>
        </div>
        <div class="summary-item">
            <span class="summary-value">Rp {{ number_format($stats['total_revenue'] ?? 0, 0, ',', '.') }}</span>
            <div class="summary-label">Total Revenue</div>
        </div>
        <div class="summary-item">
            <span class="summary-value">Rp {{ number_format($stats['total_penalty'] ?? 0, 0, ',', '.') }}</span>
            <div class="summary-label">Total Denda</div>
        </div>
        <div class="summary-item">
            <span class="summary-value">Rp {{ number_format($stats['avg_rental_value'] ?? 0, 0, ',', '.') }}</span>
            <div class="summary-label">Rata-rata Nilai</div>
        </div>
    </div>
    
    <!-- Rental Details Table -->
    @if(isset($rentals) && $rentals->count() > 0)
        <table>
            <thead>
                <tr>
                    <th width="5%">No</th>
                    <th width="12%">Tanggal</th>
                    <th width="18%">Customer</th>
                    <th width="15%">PS Unit</th>
                    <th width="8%">Durasi</th>
                    <th width="12%">Mulai</th>
                    <th width="12%">Selesai</th>
                    <th width="10%">Total</th>
                    <th width="8%">Status</th>
                </tr>
            </thead>
            <tbody>
                @foreach($rentals as $index => $rental)
                    <tr>
                        <td class="text-center">{{ $index + 1 }}</td>
                        <td>{{ \Carbon\Carbon::parse($rental->created_at)->format('d/m/Y') }}</td>
                        <td>{{ $rental->customer->name ?? 'N/A' }}</td>
                        <td>{{ $rental->psUnit->name ?? 'N/A' }}</td>
                        <td class="text-center">{{ $rental->duration }}h</td>
                        <td>{{ $rental->start_datetime ? \Carbon\Carbon::parse($rental->start_datetime)->format('d/m H:i') : '-' }}</td>
                        <td>{{ $rental->end_datetime ? \Carbon\Carbon::parse($rental->end_datetime)->format('d/m H:i') : '-' }}</td>
                        <td class="text-right">Rp {{ number_format($rental->final_amount, 0, ',', '.') }}</td>
                        <td class="text-center">
                            <span class="status-badge status-{{ $rental->status }}">
                                {{ ucfirst($rental->status) }}
                            </span>
                        </td>
                    </tr>
                @endforeach
            </tbody>
        </table>
    @else
        <div class="no-data">
            Tidak ada data rental yang sesuai dengan filter yang diterapkan.
        </div>
    @endif
    
    <!-- Footer -->
    <div class="footer">
        <p>Laporan digenerate pada: {{ now()->format('d/m/Y H:i:s') }}</p>
        <p>Rental PlayStation Management System</p>
    </div>
</body>
</html>
