<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\PsUnit;
use App\Models\Rental;
use App\Models\Customer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class MobileController extends Controller
{
    /**
     * Get dashboard data for mobile app
     */
    public function dashboard()
    {
        $stats = [
            'total_ps_units' => PsUnit::count(),
            'available_units' => PsUnit::where('available_stock', '>', 0)->count(),
            'ps_types' => PsUnit::select('ps_type')->distinct()->pluck('ps_type'),
        ];

        $featured_units = PsUnit::where('available_stock', '>', 0)
                                ->orderBy('created_at', 'desc')
                                ->limit(6)
                                ->get()
                                ->map(function ($unit) {
                                    return [
                                        'id' => $unit->id,
                                        'unit_code' => $unit->unit_code,
                                        'ps_type' => $unit->ps_type,
                                        'ps_display_name' => $unit->ps_display_name,
                                        'brand' => $unit->brand,
                                        'price_per_hour' => $unit->price_per_hour,
                                        'price_per_day' => $unit->price_per_day,
                                        'condition' => $unit->condition,
                                        'image_url' => $unit->ps_image,
                                        'available_stock' => $unit->available_stock,
                                        'total_stock' => $unit->total_stock,
                                    ];
                                });

        return response()->json([
            'success' => true,
            'data' => [
                'stats' => $stats,
                'featured_units' => $featured_units,
            ]
        ]);
    }

    /**
     * Get all PS units with filters
     */
    public function psUnits(Request $request)
    {
        $query = PsUnit::query();

        // Filter by availability
        if ($request->get('available_only', false)) {
            $query->where('available_stock', '>', 0);
        }

        // Filter by PS type
        if ($request->has('ps_type')) {
            $query->where('ps_type', 'like', '%' . $request->get('ps_type') . '%');
        }

        // Filter by price range
        if ($request->has('min_price')) {
            $query->where('price_per_hour', '>=', $request->get('min_price'));
        }
        if ($request->has('max_price')) {
            $query->where('price_per_hour', '<=', $request->get('max_price'));
        }

        // Search
        if ($request->has('search')) {
            $search = $request->get('search');
            $query->where(function($q) use ($search) {
                $q->where('unit_code', 'like', '%' . $search . '%')
                  ->orWhere('ps_type', 'like', '%' . $search . '%')
                  ->orWhere('description', 'like', '%' . $search . '%');
            });
        }

        $units = $query->orderBy('created_at', 'desc')
                      ->paginate($request->get('per_page', 10))
                      ->through(function ($unit) {
                          return [
                              'id' => $unit->id,
                              'unit_code' => $unit->unit_code,
                              'ps_type' => $unit->ps_type,
                              'ps_display_name' => $unit->ps_display_name,
                              'brand' => $unit->brand,
                              'description' => $unit->description,
                              'price_per_hour' => $unit->price_per_hour,
                              'price_per_day' => $unit->price_per_day,
                              'status' => $unit->status,
                              'condition' => $unit->condition,
                              'image_url' => $unit->ps_image,
                              'available_stock' => $unit->available_stock,
                              'total_stock' => $unit->total_stock,
                              'accessories' => $unit->accessories,
                              'is_available' => $unit->isAvailable(),
                          ];
                      });

        return response()->json([
            'success' => true,
            'data' => $units
        ]);
    }

    /**
     * Get PS unit detail
     */
    public function psUnitDetail($id)
    {
        $unit = PsUnit::find($id);

        if (!$unit) {
            return response()->json([
                'success' => false,
                'message' => 'PS Unit not found'
            ], 404);
        }

        return response()->json([
            'success' => true,
            'data' => [
                'id' => $unit->id,
                'unit_code' => $unit->unit_code,
                'ps_type' => $unit->ps_type,
                'ps_display_name' => $unit->ps_display_name,
                'brand' => $unit->brand,
                'description' => $unit->description,
                'price_per_hour' => $unit->price_per_hour,
                'price_per_day' => $unit->price_per_day,
                'status' => $unit->status,
                'condition' => $unit->condition,
                'image_url' => $unit->ps_image,
                'available_stock' => $unit->available_stock,
                'total_stock' => $unit->total_stock,
                'accessories' => $unit->accessories,
                'purchase_date' => $unit->purchase_date,
                'is_available' => $unit->isAvailable(),
                'stock_status' => $unit->stock_status,
            ]
        ]);
    }

    /**
     * Customer registration
     */
    public function registerCustomer(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:customers',
            'phone' => 'required|string|max:20',
            'address' => 'required|string',
            'password' => 'required|string|min:6|confirmed',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $customer = Customer::create([
            'name' => $request->name,
            'email' => $request->email,
            'phone' => $request->phone,
            'address' => $request->address,
            'password' => Hash::make($request->password),
        ]);

        return response()->json([
            'success' => true,
            'message' => 'Customer registered successfully',
            'data' => [
                'id' => $customer->id,
                'name' => $customer->name,
                'email' => $customer->email,
                'phone' => $customer->phone,
            ]
        ], 201);
    }

    /**
     * Customer login
     */
    public function loginCustomer(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'success' => false,
                'message' => 'Validation failed',
                'errors' => $validator->errors()
            ], 422);
        }

        $customer = Customer::where('email', $request->email)->first();

        if (!$customer) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid credentials'
            ], 401);
        }

        // Check if password exists and is valid
        if (empty($customer->password) || !is_string($customer->password)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid credentials'
            ], 401);
        }

        if (!Hash::check($request->password, (string) $customer->password)) {
            return response()->json([
                'success' => false,
                'message' => 'Invalid credentials'
            ], 401);
        }

        // Create token using Laravel Sanctum
        $token = $customer->createToken('mobile-app')->plainTextToken;

        return response()->json([
            'success' => true,
            'message' => 'Login successful',
            'data' => [
                'customer' => [
                    'id' => $customer->id,
                    'name' => $customer->name,
                    'email' => $customer->email,
                    'phone' => $customer->phone,
                    'address' => $customer->address,
                ],
                'token' => $token,
            ]
        ]);
    }
}
